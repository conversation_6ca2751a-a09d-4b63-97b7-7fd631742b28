﻿
namespace Common
{   
    public class Images
    {
        public string Key { get; set; }
        public System.Drawing.Image Image { get; set; }
    }

    public class Blocks
    {
        public int Num { get; set; }
        public byte Tag { get; set; }
        public int Size { get; set; }
        public int Level { get; set; }
        public int Offset { get; set; }
        public string Pos { get; set; }
        public bool Directory { get; set; }
    }
}
