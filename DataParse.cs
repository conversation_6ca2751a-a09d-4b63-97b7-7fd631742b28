﻿using Common;
using System;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace DataParser
{
    public class EDID_FUNC
    {
        private readonly string[] Vic_4K =
        {
            "Reserved",
            "4K x 2K 29.97/30Hz",
            "4K x 2K 25Hz",
            "4K x 2K 23.98/24Hz",
            "4K x 2K 24Hz(SMPTE)",
        };

        private readonly string[] Vic_List =
        {
            "No Video Identification Code",
            "640x480p @ 59.94/60Hz 4:3",
            "720x480p @ 59.94/60Hz 4:3",
            "720x480p @ 59.94/60Hz 16:9",
            "1280x720p @ 59.94/60Hz 16:9",
            "1920x1080i @ 59.94/60Hz 16:9",
            "720(1440)x480i @ 59.94/60Hz 4:3",
            "720(1440)x480i @ 59.94/60Hz 16:9",
            "720(1440)x240p @ 59.94/60Hz 4:3",
            "720(1440)x240p @ 59.94/60Hz 16:9",
            "2880x480i @ 59.94/60Hz 4:3",
            "2880x480i @ 59.94/60Hz 16:9",
            "2880x240p @ 59.94/60Hz 4:3",
            "2880x240p @ 59.94/60Hz 16:9",
            "1440x480p @ 59.94/60Hz 4:3",
            "1440x480p @ 59.94/60Hz 16:9",
            "1920x1080p @ 59.94/60Hz 16:9",
            "720x576p @ 50Hz 4:3",
            "720x576p @ 50Hz 16:9",
            "1280x720p @ 50Hz 16:9",
            "1920x1080i @ 50Hz 16:9",
            "720(1440)x576i @ 50Hz 4:3",
            "720(1440)x576i @ 50Hz 16:9",
            "720(1440)x288p @ 50Hz 4:3",
            "720(1440)x288p @ 50Hz 16:9",
            "2880x576i @ 50Hz 4:3",
            "2880x576i @ 50Hz 16:9",
            "2880x288p @ 50Hz 4:3",
            "2880x288p @ 50Hz 16:9",
            "1440x576p @ 50Hz 4:3",
            "1440x576p @ 50Hz 16:9",
            "1920x1080p @ 50Hz 16:9",
            "1920x1080p @ 23.98/24Hz 16:9",
            "1920x1080p @ 25Hz 16:9",
            "1920x1080p @ 29.97/30Hz 16:9",
            "2880x480p @ 59.94/60Hz 4:3",
            "2880x480p @ 59.94/60Hz 16:9",
            "2880x576p @ 50Hz 4:3",
            "2880x576p @ 50Hz 16:9",
            "1920x1080i (1250 total) @ 50Hz 16:9",
            "1920x1080i @ 100Hz 16:9",
            "1280x720p @ 100Hz 16:9",
            "720x576p @ 100Hz 4:3",
            "720x576p @ 100Hz 16:9",
            "720(1440)x576i @ 100Hz 4:3",
            "720(1440)x576i @ 100Hz 16:9",
            "1920x1080i @ 119.88/120Hz 16:9",
            "1280x720p @ 119.88/120Hz 16:9",
            "720x480p @ 119.88/120Hz 4:3",
            "720x480p @ 119.88/120Hz 16:9",
            "720(1440)x480i @ 119.88/120Hz 4:3",
            "720(1440)x480i @ 119.88/120Hz 16:9",
            "720x576p @ 200Hz 4:3",
            "720x576p @ 200Hz 16:9",
            "720(1440)x576i @ 200Hz 4:3",
            "720(1440)x576i @ 200Hz 16:9",
            "720x480p @ 239.76/240Hz 4:3",
            "720x480p @ 239.76/240Hz 16:9",
            "720(1440)x480i @ 239.76/240Hz 4:3",
            "720(1440)x480i @ 239.76/240Hz 16:9",
            "1280x720p @ 23.98/24Hz 16:9",
            "1280x720p @ 25Hz 16:9",
            "1280x720p @ 29.97/30Hz 16:9",
            "1920x1080p @ 119.88/120Hz 16:9",
            "1920x1080p @ 100Hz 16:9",
            "1280x720p @ 23.98/24Hz 64:276",
            "1280x720p @ 25Hz 64:276",
            "1280x720p @ 29.97/30Hz 64:276",
            "1280x720p @ 50Hz 64:276",
            "1280x720p @ 59.94/60Hz 64:276",
            "1280x720p @ 100Hz 64:276",
            "1280x720p @ 119.88/120Hz 64:276",
            "1920x1080p @ 23.98/24Hz 64:276",
            "1920x1080p @ 25Hz 64:276",
            "1920x1080p @ 29.97Hz/30Hz 64:276",
            "1920x1080p @ 50Hz 64:276",
            "1920x1080p @ 59.94/60Hz 64:276",
            "1920x1080p @ 100Hz 64:276",
            "1920x1080p @ 119.88/120Hz 64:276",
            "1680x720p @ 23.98/24Hz 64:276",
            "1680x720p @ 25Hz 64:276",
            "1680x720p @ 29.97/30Hz   64:276",
            "1680x720p @ 50Hz 64:276",
            "1680x720p @ 59.94/60Hz 64:276",
            "1680x720p @ 100Hz 64:276",
            "1680x720p @ 119.88/120Hz 64:276",
            "2560x1080p @ 23.98/24Hz 64:276",
            "2560x1080p @ 25Hz 64:276",
            "2560x1080p @ 29.97Hz/30Hz   64:276",
            "2560x1080p @ 50Hz 64:276",
            "2560x1080p @ 59.94/60Hz 64:276",
            "2560x1080p @ 100Hz 64:276",
            "2560x1080p @ 119.88/120Hz 64:276",
            "3840x2160p @ 23.98/24Hz 16:9",
            "3840x2160p @ 25Hz 16:9",
            "3840x2160p @ 29.97/30Hz 16:9",
            "3840x2160p @ 50Hz 16:9",
            "3840x2160p @ 59.94/60Hz 16:9",
            "4096x2160p @ 23.98/24Hz 256:135",
            "4096x2160p @ 25Hz 256:135",
            "4096x2160p @ 29.97/30Hz 256:135",
            "4096x2160p @ 50Hz 256:135",
            "4096x2160p @ 59.94/60Hz 256:135",
            "3840x2160p @ 23.98/24Hz 64:276",
            "3840x2160p @ 25Hz 64:276",
            "3840x2160p @ 29.97/30Hz 64:276",
            "3840x2160p @ 50Hz 64:276",
            "3840x2160p @ 59.94/60Hz 64:276",
            "1280x720p @ 47.95/48Hz 16:9",
            "1280x720p @ 47.95/48Hz 64:276",
            "1680x720p @ 47.95/48Hz 64:276",
            "1920x1080p @ 47.95/48Hz 16:9",
            "1920x1080p @ 47.95/48Hz 64:276",
            "2560x1080p @ 47.95/48Hz 64:276",
            "3840x2160p @ 47.95/48Hz 16:9",
            "4096x2160p @ 47.95/48Hz 256:135",
            "3840x2160p @ 47.95/48Hz 64:276",
            "3840x2160p @ 100Hz 16:9",
            "3840x2160p @ 119.88/120Hz 16:9",
            "3840x2160p @ 100Hz 64:276",
            "3840x2160p @ 119.88/120Hz 64:276",
            "5120x2160p @ 23.98/24Hz 64:276",
            "5120x2160p @ 25Hz 64:276",
            "5120x2160p @ 29.97/30Hz 64:276",
            "5120x2160p @ 47.95/48Hz 64:276",
            "5120x2160p @ 50Hz 64:276",
            "5120x2160p @ 59.94/60Hz 64:276",
            "5120x2160p @ 100Hz 64:276",
            "Forbidden",//128~192
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "Forbidden",
            "5120x2160p @ 119.88/120Hz 64:276",
            "7680x4320p @ 23.98/24Hz 16:9",
            "7680x4320p @ 25Hz 16:9",
            "7680x4320p @ 29.97/30Hz 16:9",
            "7680x4320p @ 47.95/48Hz 16:9",
            "7680x4320p @ 50Hz 16:9",
            "7680x4320p @ 59.94/60Hz 16:9",
            "7680x4320p @ 100Hz 16:9",
            "7680x4320p @ 119.88/120Hz 16:9",
            "7680x4320p @ 23.98/24Hz 64:276",
            "7680x4320p @ 25Hz 64:276",
            "7680x4320p @ 29.97/30Hz 64:276",
            "7680x4320p @ 47.95/48Hz 64:276",
            "7680x4320p @ 50Hz 64:276",
            "7680x4320p @ 59.94/60Hz 64:276",
            "7680x4320p @ 100Hz 64:276",
            "7680x4320p @ 119.88/120Hz 64:276",
            "10240x4320p @ 23.98/24Hz 64:276",
            "10240x4320p @ 25Hz 64:276",
            "10240x4320p @ 29.97/30Hz 64:276",
            "10240x4320p @ 47.95/48Hz 64:276",
            "10240x4320p @ 50Hz 64:276",
            "10240x4320p @ 59.94/60Hz 64:276",
            "10240x4320p @ 100Hz 64:276",
            "10240x4320p @ 119.88/120Hz 64:276",
            "4096x2160p @ 100Hz 256:135",
            "4096x2160p @ 119.88/120Hz 256:135",
            "Reserved for the Future",//220~255
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
            "Reserved for the Future",
        };

        public void EDID_Parsetext(byte[] edid, TreeNode node, TextBox text, bool select)
        {
            byte[] data;
            Blocks block = (Blocks)node.Tag;

            if (select) data = edid.Skip(block.Num * 128).Take(128).ToArray();
            else data = edid.Skip(block.Offset).Take(block.Size).ToArray();

            foreach (byte b in data)
            {
                text.Text += b.ToString("X2") + ' ';
            }

            if (select) text.Select((block.Offset % 128) * 3, block.Size * 3 - 1);
        }

        private void EDID_Filldata(string name, string value, DataGridView gridView)
        {
            int index = gridView.Rows.Add();

            gridView.Rows[index].Cells[0].Value = name;
            gridView.Rows[index].Cells[0].Style.Alignment = DataGridViewContentAlignment.MiddleLeft;
            gridView.Rows[index].Cells[0].Style.BackColor = System.Drawing.SystemColors.Control;

            gridView.Rows[index].Cells[1].Value = value;
            gridView.Rows[index].Cells[1].Style.BackColor = System.Drawing.SystemColors.Control;
            gridView.Rows[index].Cells[1].Style.Alignment = DataGridViewContentAlignment.MiddleCenter;

            gridView.CurrentCell = gridView.Rows[0].Cells[0];
            gridView.ClearSelection();
        }

        private int Block_Offset(int block, byte[] edid, byte tag, int start)
        {
            int i;

            if (edid[126] >= 1)
            {
                int dtd_pos = edid[block * 128 + 2] + block * 128;

                for (i = start; i < dtd_pos; i++)
                {
                    if ((edid[i] & 0xE0) == tag)
                        return i;
                    else
                        i += (edid[i] & 0x1F);
                }

                if (i > dtd_pos) return 0;
            }

            return 0;
        }

        private void Header_Info(byte tag, byte[] data, DataGridView gridView)
        {
            StringBuilder stringBuilder = new StringBuilder();

            if (tag == 0)
            {
                foreach (byte b in data)
                {
                    stringBuilder.Append(b.ToString("X2") + ' ');
                }
                EDID_Filldata("Header", stringBuilder.ToString(), gridView);
            }
            else
            if (tag == 2)
            {
                EDID_Filldata("Extension Tag", Convert.ToDecimal(data[0]).ToString(), gridView);
                EDID_Filldata("Revision", Convert.ToDecimal(data[1]).ToString(), gridView);
                EDID_Filldata("DTD Offset", Convert.ToDecimal(data[2]).ToString(), gridView);

                if (data[1] > 1)
                {
                    EDID_Filldata("Underscan", ((data[3] & 0x80) == 0) ? "N" : "Y", gridView);
                    EDID_Filldata("Basic Audio", ((data[3] & 0x40) == 0) ? "N" : "Y", gridView);
                    EDID_Filldata("YCbCr4:4:4", ((data[3] & 0x20) == 0) ? "N" : "Y", gridView);
                    EDID_Filldata("YCbCr4:2:2", ((data[3] & 0x10) == 0) ? "N" : "Y", gridView);
                    EDID_Filldata("Number of Native DTD", (data[3] & 0x0F).ToString(), gridView);
                }
            }
        }

        private void Vendor_Information(byte[] data, DataGridView gridView)
        {
            StringBuilder stringBuilder = new StringBuilder();

            byte[] char_array = new byte[3];
            //ch1
            byte location = (byte)((data[0] & 0x7C) >> 2);
            char_array[0] = (byte)(location - 1 + 'A');

            //ch2
            location = (byte)(((data[0] & 0x03) << 3) | ((data[1] & 0xE0) >> 5));
            char_array[1] = (byte)(location - 1 + 'A');

            //ch3
            location = (byte)(data[1] & 0x1F);
            char_array[2] = (byte)(location - 1 + 'A');

            stringBuilder.Append(Convert.ToChar(char_array[0]).ToString());
            stringBuilder.Append(Convert.ToChar(char_array[1]).ToString());
            stringBuilder.Append(Convert.ToChar(char_array[2]).ToString());

            EDID_Filldata("Manufacture Name", stringBuilder.ToString(), gridView);
            EDID_Filldata("Product Code", data[3].ToString("X2") + data[2].ToString("X2"), gridView);
            EDID_Filldata("Serial Number", data[7].ToString("X2") + data[6].ToString("X2") + data[5].ToString("X2") + data[4].ToString("X2"), gridView);

            if (data[8] <= 0xFE)
            {
                EDID_Filldata("Week of Manufacture", Convert.ToDecimal(data[8]).ToString(), gridView);
                EDID_Filldata("Year of Manufacture (1990+)", (Convert.ToDecimal(data[9] + 1990)).ToString(), gridView);
            }
            else
            {
                EDID_Filldata("Model Year Flag", "specified in next field", gridView);
                EDID_Filldata("Model Year (1990+)", (Convert.ToDecimal(data[9] + 1990)).ToString(), gridView);
            }
        }

        private void Video_Input_Definition(byte[] data, DataGridView gridView)
        {
            bool digital = Convert.ToBoolean(data[0] & 0x80);

            if (digital)
            {
                var color_depth = (data[0] & 0x70) >> 4;
                var inter_face = data[0] & 0x0F;

                switch (color_depth)
                {
                    case 0:
                        EDID_Filldata("Color Depth", "Undefined", gridView);
                        break;

                    case 1:
                        EDID_Filldata("Color Depth", "6bit", gridView);
                        break;

                    case 2:
                        EDID_Filldata("Color Depth", "8bit", gridView);
                        break;

                    case 3:
                        EDID_Filldata("Color Depth", "10bit", gridView);
                        break;

                    case 4:
                        EDID_Filldata("Color Depth", "12bit", gridView);
                        break;

                    case 5:
                        EDID_Filldata("Color Depth", "14bit", gridView);
                        break;

                    case 6:
                        EDID_Filldata("Color Depth", "16bit", gridView);
                        break;

                    default:
                        EDID_Filldata("Color Depth", "-", gridView);
                        break;
                }

                switch (inter_face)
                {
                    case 0:
                        EDID_Filldata("Digital Video Interface Standard", "Undefined", gridView);
                        break;

                    case 1:
                        EDID_Filldata("Digital Video Interface Standard", "DVI", gridView);
                        break;

                    case 2:
                        EDID_Filldata("Digital Video Interface Standard", "HDMI-a", gridView);
                        break;

                    case 3:
                        EDID_Filldata("Digital Video Interface Standard", "HDMI-b", gridView);
                        break;

                    case 4:
                        EDID_Filldata("Digital Video Interface Standard", "MDDI", gridView);
                        break;

                    case 5:
                        EDID_Filldata("Digital Video Interface Standard", "DisplayPort", gridView);
                        break;

                    default:
                        EDID_Filldata("Digital Video Interface Standard", "-", gridView);
                        break;
                }
            }
            else
            {
                //14[6:5]
                var signal_level = (data[0] & 0x60) >> 5;

                switch (signal_level)
                {
                    default:
                    case 0:
                        EDID_Filldata("Signal Level Standard(Video:Sync:Total)", "0.700 : 0.300 : 1.0Vpp", gridView);
                        break;

                    case 1:
                        EDID_Filldata("Signal Level Standard(Video:Sync:Total)", "0.714 : 0.286 : 1.0Vpp", gridView);
                        break;

                    case 2:
                        EDID_Filldata("Signal Level Standard(Video:Sync:Total)", "1.000 : 0.400 : 1.4Vpp", gridView);
                        break;

                    case 3:
                        EDID_Filldata("Signal Level Standard(Video:Sync:Total)", "0.700 : 0.000 : 0.7Vpp", gridView);
                        break;
                }

                //14[4]
                EDID_Filldata("Video Setup", ((data[0] & 0x10) == 0) ? "Blank-to-Black setup or pedestal" : "Blank Level = Black Level", gridView);

                //14[3]
                EDID_Filldata("Separate Sync H & V Signals", ((data[0] & 0x08) == 0) ? "N" : "Y", gridView);

                //14[2]
                EDID_Filldata("Composite Sync Signal on Horizontal", ((data[0] & 0x04) == 0) ? "N" : "Y", gridView);

                //14[1]
                EDID_Filldata("Composite Sync Signal on Green Video", ((data[0] & 0x02) == 0) ? "N" : "Y", gridView);

                //14[0]
                EDID_Filldata("Serration on the Vertical Sync", ((data[0] & 0x01) == 0) ? "N" : "Y", gridView);
            }
        }

        private void Hors_Vert_Screen_Size(byte[] data, DataGridView gridView)
        {
            //15,16
            if (data[0] > 0)
            {
                if (data[1] > 0)
                {
                    EDID_Filldata("Horizontal Screen Size", Convert.ToDecimal(data[0]).ToString() + " cm", gridView);
                    EDID_Filldata("Vertical Screen Size", Convert.ToDecimal(data[1]).ToString() + " cm", gridView);
                }
                else
                {
                    EDID_Filldata("Aspect Ratio (Landscape)", Convert.ToDouble((data[1] + 99.0f) / 100.0f).ToString("0.000"), gridView);
                }
            }
            else
            {
                if (data[1] > 0)
                {
                    EDID_Filldata("Aspect Ratio (Portrait)", Convert.ToDouble(100.0f / (data[1] + 99.0f)).ToString("0.000"), gridView);
                }
                else
                {
                    EDID_Filldata("Screen Size/Aspect Ratio", "Unknown/Undefined", gridView);
                }
            }
        }

        private void Display_Transfer_Characteristic(byte[] data, DataGridView gridView)
        {
            //17
            if (data[0] == 0)
            {
                EDID_Filldata("Gamma", "Undefined", gridView);
            }
            else
            if (data[0] == 0xFF)
            {
                EDID_Filldata("Gamma", "shall be stored in an extension block", gridView);
            }
            else
            {
                EDID_Filldata("Gamma", Convert.ToDouble((data[0] + 100.0f) / 100.0f).ToString("0.00"), gridView);
            }
        }

        private void Feature_Support(byte[] edid, byte[] data, DataGridView gridView)
        {
            //18[7]
            EDID_Filldata("Standby", ((data[0] & 0x80) == 0) ? "N" : "Y", gridView);
            //18[6]
            EDID_Filldata("Suspend", ((data[0] & 0x40) == 0) ? "N" : "Y", gridView);
            //18[5]
            EDID_Filldata("Active Off", ((data[0] & 0x20) == 0) ? "N" : "Y", gridView);

            //18[4:3]
            if ((edid[0x14] & 0x80) == 0)
            {
                switch ((data[0] & 0x18) >> 3)
                {
                    case 0:
                        EDID_Filldata("Display Type", "Monochrome", gridView);
                        break;

                    case 1:
                        EDID_Filldata("Display Type", "RGB", gridView);
                        break;

                    case 2:
                        EDID_Filldata("Display Type", "Non-RGB", gridView);
                        break;

                    case 3:
                        EDID_Filldata("Display Type", "Undefined", gridView);
                        break;
                }
            }
            else
            {
                switch ((data[0] & 0x18) >> 3)
                {
                    case 0:
                        EDID_Filldata("Color Space", "RGB 4:4:4", gridView);
                        break;

                    case 1:
                        EDID_Filldata("Color Space", "RGB 4:4:4 & YCbCr 4:4:4", gridView);
                        break;

                    case 2:
                        EDID_Filldata("Color Space", "RGB 4:4:4 & YCbCr 4:2:2", gridView);
                        break;

                    case 3:
                        EDID_Filldata("Color Space", "RGB 4:4:4 & YCbCr 4:4:4 & YCbCr 4:2:2", gridView);
                        break;
                }
            }

            //18[2]
            EDID_Filldata("sRGB Standard", ((data[0] & 0x04) == 0) ? "N" : "Y", gridView);
            //18[1]
            EDID_Filldata("Preferred Timing", ((data[0] & 0x02) == 0) ? "N" : "Y", gridView);
            //18[0]
            EDID_Filldata("Default GTF", ((data[0] & 0x01) == 0) ? "N" : "Y", gridView);
        }

        private double Color_Calc(int data)
        {
            double x = 0;

            if ((data & 0x200) != 0) x += Math.Pow(2.0f, -1.0f);
            if ((data & 0x100) != 0) x += Math.Pow(2.0f, -2.0f);
            if ((data & 0x80) != 0) x += Math.Pow(2.0f, -3.0f);
            if ((data & 0x40) != 0) x += Math.Pow(2.0f, -4.0f);
            if ((data & 0x20) != 0) x += Math.Pow(2.0f, -5.0f);
            if ((data & 0x10) != 0) x += Math.Pow(2.0f, -6.0f);
            if ((data & 0x08) != 0) x += Math.Pow(2.0f, -7.0f);
            if ((data & 0x04) != 0) x += Math.Pow(2.0f, -8.0f);
            if ((data & 0x02) != 0) x += Math.Pow(2.0f, -9.0f);
            if ((data & 0x01) != 0) x += Math.Pow(2.0f, -10.0f);

            return x;
        }

        private void Color_Characteristics(byte[] data, DataGridView gridView)
        {
            //19, red/green low bits, Rx1 Rx0 Ry1 Ry0 Gx1 Gx0 Gy1 Gy0
            //1a, blue/white low bits, Bx1 Bx0 By1 By0 Wx1 Wx0 Wy1 Wy0
            //1b, red_x
            //1c, red_y
            //1d, green_x
            //1e, green_y
            //1f, blue_x
            //20, blue_y
            //21, white_x
            //22, white_y
            int Rx, Ry, Gx, Gy, Bx, By, Wx, Wy;

            Rx = (data[2] << 2) | ((data[0] & 0xC0) >> 6);
            Ry = (data[3] << 2) | ((data[0] & 0x30) >> 4);
            Gx = (data[4] << 2) | ((data[0] & 0x0C) >> 2);
            Gy = (data[5] << 2) | (data[0] & 0x03);
            Bx = (data[6] << 2) | ((data[1] & 0xC0) >> 6);
            By = (data[7] << 2) | ((data[1] & 0x30) >> 4);
            Wx = (data[8] << 2) | ((data[1] & 0x0C) >> 2);
            Wy = (data[9] << 2) | (data[1] & 0x03);

            EDID_Filldata("Red(x,y)", Color_Calc(Rx).ToString("0.0000") + ", " + Color_Calc(Ry).ToString("0.0000"), gridView);
            EDID_Filldata("Green(x,y)", Color_Calc(Gx).ToString("0.0000") + ", " + Color_Calc(Gy).ToString("0.0000"), gridView);
            EDID_Filldata("Black(x,y)", Color_Calc(Bx).ToString("0.0000") + ", " + Color_Calc(By).ToString("0.0000"), gridView);
            EDID_Filldata("White(x,y)", Color_Calc(Wx).ToString("0.0000") + ", " + Color_Calc(Wy).ToString("0.0000"), gridView);
        }

        private void Established_Timing(byte[] data, DataGridView gridView)
        {
            //23, Timing I
            EDID_Filldata("720 x 400 @ 70Hz (IBM, VGA)", ((data[0] & 0x80) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("720 x 400 @ 88Hz (IBM, XGA2)", ((data[0] & 0x40) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("640 x 480 @ 60Hz (IBM, VGA)", ((data[0] & 0x20) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("640 x 480 @ 67Hz (Apple, Mac II)", ((data[0] & 0x10) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("640 x 480 @ 72Hz (VESA)", ((data[0] & 0x08) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("640 x 480 @ 75Hz (VESA)", ((data[0] & 0x04) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("800 x 600 @ 56Hz (VESA)", ((data[0] & 0x02) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("800 x 600 @ 60Hz (VESA)", ((data[0] & 0x01) == 0) ? "N" : "Y", gridView);

            //24, Timing II
            EDID_Filldata("800 x 600 @ 72Hz (VESA)", ((data[1] & 0x80) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("800 x 600 @ 75Hz (VESA)", ((data[1] & 0x40) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("832 x 624 @ 75Hz (Apple, Mac II)", ((data[1] & 0x20) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("1024 x 768 @ 87Hz(I) (IBM - Interlaced)", ((data[1] & 0x10) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("1024 x 768 @ 60Hz (VESA)", ((data[1] & 0x08) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("1024 x 768 @ 70Hz (VESA)", ((data[1] & 0x04) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("1024 x 768 @ 75Hz (VESA)", ((data[1] & 0x02) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("1280 x 1024 @ 75Hz (VESA)", ((data[1] & 0x01) == 0) ? "N" : "Y", gridView);

            //25, manufacturer's timing
            EDID_Filldata("1152 x 870 @ 75Hz (Apple, Mac II)", ((data[2] & 0x80) == 0) ? "N" : "Y", gridView);
        }

        private void Standard_Timing(byte[] data, DataGridView gridView)
        {
            if (data[0] == 0x01 && data[1] == 0x01)
            {
                EDID_Filldata("Unused", "-", gridView); return;
            }

            if (data[0] == 0)
            {
                EDID_Filldata("H-Active pixels", "-", gridView);
            }
            else
            {
                int pixel = (data[0] + 31) * 8;
                EDID_Filldata("H-Active Pixels", Convert.ToDecimal(pixel).ToString(), gridView);
            }

            int ar = (data[1] & 0xC0) >> 6;
            switch (ar)
            {
                case 0:
                    EDID_Filldata("Image Aspect Ration", "16:10", gridView);
                    break;

                case 1:
                    EDID_Filldata("Image Aspect Ration", "4:3", gridView);
                    break;

                case 2:
                    EDID_Filldata("Image Aspect Ration", "5:4", gridView);
                    break;

                case 3:
                    EDID_Filldata("Image Aspect Ration", "16:9", gridView);
                    break;
            }

            int fr = (data[1] & 0x3F) + 60;
            EDID_Filldata("Refresh Rate", Convert.ToDecimal(fr).ToString() + " Hz", gridView);
        }

        //refer: VESA E-EDID Release A Rev2 3.10
        private void Detailed_Timing_Descriptor(bool prefer, byte[] data, DataGridView gridView)
        {
            if (data[0] == 0x00 && data[1] == 0x00 && data[2] == 0x00)
            {
                byte tag = data[3];

                switch (tag)
                {
                    case 0xFF://Display Product Serial Number Descriptor Definition
                        EDID_Filldata("Product Serial Number", "-", gridView);
                        break;

                    case 0xFE://Alphanumeric Data String Descriptor Definition
                        EDID_Filldata("Alphanumeric Data String", "-", gridView);
                        break;

                    case 0xFD://Display Range Limits & Additional Timing Descriptor Definition
                        EDID_Filldata("Min. Vertical Rate", Convert.ToDecimal(data[5]).ToString() + " Hz", gridView);
                        EDID_Filldata("Max. Vertical Rate", Convert.ToDecimal(data[6]).ToString() + " Hz", gridView);
                        EDID_Filldata("Min. Horizontal Rate", Convert.ToDecimal(data[7]).ToString() + " KHz", gridView);
                        EDID_Filldata("Min. Horizontal Rate", Convert.ToDecimal(data[8]).ToString() + " KHz", gridView);
                        EDID_Filldata("Max. Pixel Clock", Convert.ToDecimal(data[9] * 10).ToString() + " MHz", gridView);
                        switch (data[10])
                        {
                            case 0:
                                if (prefer) EDID_Filldata("Video Timing Support Flag", "Default GTF Supported", gridView);
                                break;

                            case 1:
                                EDID_Filldata("Video Timing Support Flag", "Range Limits Only", gridView);
                                break;

                            case 2:
                                EDID_Filldata("Video Timing Support Flag", "Secondary GTF Supported", gridView);
                                EDID_Filldata("Secondary Curve Start Freq", Convert.ToDecimal(data[12]).ToString() + " KHz", gridView);
                                EDID_Filldata("Cx2", Convert.ToDecimal(data[13]).ToString(), gridView);
                                EDID_Filldata("M", Convert.ToDecimal((data[15] << 8) | data[14]).ToString(), gridView);
                                EDID_Filldata("K", Convert.ToDecimal(data[16]).ToString(), gridView);
                                EDID_Filldata("Jx2", Convert.ToDecimal(data[17]).ToString(), gridView);
                                break;

                            case 4:
                                EDID_Filldata("Video Timing Support Flag", "CVT Supported", gridView);
                                EDID_Filldata("CVT Version", Convert.ToDecimal((data[11] & 0xF0) >> 4).ToString() + "." + Convert.ToDecimal(data[11] & 0x0F).ToString(), gridView);
                                break;
                        }
                        break;

                    case 0xFC://Display Product Name (ASCII) String Descriptor Definition
                        StringBuilder stringBuilder = new StringBuilder();
                        for (int i = 5; i < data.Length; i++)
                        {
                            if (data[i] == 0x0A) break;
                            stringBuilder.Append(Convert.ToChar(data[i]).ToString());
                        }
                        EDID_Filldata("Product Name (ASCII)", stringBuilder.ToString(), gridView);
                        stringBuilder.Clear();
                        break;

                    case 0xFB://Color Point Descriptor Definition
                        EDID_Filldata("Color Point Descriptor", "-", gridView);
                        break;

                    case 0xFA://Standard Timing Identifier Definition
                        EDID_Filldata("Standard Timing Identifier", "-", gridView);
                        break;

                    case 0xF9://Color Management Data Definition
                        EDID_Filldata("Color Management Data", "-", gridView);
                        break;

                    case 0xF8://CVT 3 Byte Code Descriptor Definition
                        EDID_Filldata("CVT 3 Byte Code Descriptor", "-", gridView);
                        break;

                    case 0xF7://Established Timings III Descriptor Definition
                        EDID_Filldata("Established Timings III", "-", gridView);
                        break;

                    case 0x10://Dummy Descriptor Definition
                        EDID_Filldata("Dummy Descriptor", "-", gridView);
                        break;

                    case 0x00://Manufacturer Specified Data Tag Numbers
                    case 0x01:
                    case 0x02:
                    case 0x03:
                    case 0x04:
                    case 0x05:
                    case 0x06:
                    case 0x07:
                    case 0x08:
                    case 0x09:
                    case 0x0A:
                    case 0x0B:
                    case 0x0C:
                    case 0x0D:
                    case 0x0E:
                    case 0x0F:
                        EDID_Filldata("Manufacturer Specified Data", "tag = " + tag.ToString("X2"), gridView);
                        break;

                    default://Unused or Reserved Data Tag Number 0x11~0xF6
                        EDID_Filldata("Unused", "-", gridView);
                        break;
                }
            }
            else
            {
                StringBuilder stringBuilder = new StringBuilder();

                var temp_data = ((data[1] << 8) | data[0]) / 100.0f;
                stringBuilder.Append(temp_data.ToString("0.000"));
                stringBuilder.Append(" MHz");
                EDID_Filldata("Pixel Clock", stringBuilder.ToString(), gridView);

                temp_data = ((data[4] & 0xF0) << 4) | data[2];
                stringBuilder.Clear();
                stringBuilder.Append(temp_data.ToString());
                stringBuilder.Append(" pixels");
                EDID_Filldata("H Active", stringBuilder.ToString(), gridView);

                temp_data = ((data[4] & 0x0F) << 8) | data[3];
                stringBuilder.Clear();
                stringBuilder.Append(temp_data.ToString());
                stringBuilder.Append(" pixels");
                EDID_Filldata("H Blank", stringBuilder.ToString(), gridView);

                temp_data = ((data[7] & 0xF0) << 4) | data[5];
                stringBuilder.Clear();
                stringBuilder.Append(temp_data.ToString());
                stringBuilder.Append(" lines");
                EDID_Filldata("V Active", stringBuilder.ToString(), gridView);

                temp_data = ((data[7] & 0x0F) << 8) | data[6];
                stringBuilder.Clear();
                stringBuilder.Append(temp_data.ToString());
                stringBuilder.Append(" lines");
                EDID_Filldata("V Blank", stringBuilder.ToString(), gridView);

                temp_data = ((data[11] & 0xC0) << 2) | data[8];
                stringBuilder.Clear();
                stringBuilder.Append(temp_data.ToString());
                stringBuilder.Append(" pixels");
                EDID_Filldata("H Front Porch", stringBuilder.ToString(), gridView);

                temp_data = ((data[11] & 0x30) << 4) | data[9];
                stringBuilder.Clear();
                stringBuilder.Append(temp_data.ToString());
                stringBuilder.Append(" pixels");
                EDID_Filldata("H Sync Width", stringBuilder.ToString(), gridView);

                temp_data = ((data[11] & 0x0C) << 2) | ((data[10] & 0xF0) >> 4);
                stringBuilder.Clear();
                stringBuilder.Append(temp_data.ToString());
                stringBuilder.Append(" lines");
                EDID_Filldata("V Front Porch", stringBuilder.ToString(), gridView);

                temp_data = ((data[11] & 0x03) << 4) | (data[10] & 0x0F);
                stringBuilder.Clear();
                stringBuilder.Append(temp_data.ToString());
                stringBuilder.Append(" lines");
                EDID_Filldata("V Sync Width", stringBuilder.ToString(), gridView);

                temp_data = ((data[14] & 0xF0) << 4) | data[12];
                stringBuilder.Clear();
                stringBuilder.Append(temp_data.ToString());
                stringBuilder.Append(" mm");
                EDID_Filldata("H Image Size", stringBuilder.ToString(), gridView);

                temp_data = ((data[14] & 0x0F) << 8) | data[13];
                stringBuilder.Clear();
                stringBuilder.Append(temp_data.ToString());
                stringBuilder.Append(" mm");
                EDID_Filldata("V Image Size", stringBuilder.ToString(), gridView);

                temp_data = data[15];
                stringBuilder.Clear();
                stringBuilder.Append(temp_data.ToString());
                stringBuilder.Append(" pixels");
                EDID_Filldata("H Border", stringBuilder.ToString(), gridView);

                temp_data = data[16];
                stringBuilder.Clear();
                stringBuilder.Append(temp_data.ToString());
                stringBuilder.Append(" lines");
                EDID_Filldata("V Border", stringBuilder.ToString(), gridView);

                EDID_Filldata("Interlaced", ((data[17] & 0x80) == 0) ? "N" : "Y", gridView);

                temp_data = ((data[17] & 0x60) >> 4) | (data[17] & 0x01);
                switch (temp_data)
                {
                    case 0:
                    case 1:
                        EDID_Filldata("Stereo", "No Stereo", gridView);
                        break;

                    case 2:
                        EDID_Filldata("Stereo", "Seq Stereo, Right Sync", gridView);
                        break;

                    case 3:
                        EDID_Filldata("Stereo", "2-Way Interleaved, Right Image", gridView);
                        break;

                    case 4:
                        EDID_Filldata("Stereo", "Seq Stereo, Left Sync", gridView);
                        break;

                    case 5:
                        EDID_Filldata("Stereo", "2-Way Interleaved, Left Image", gridView);
                        break;

                    case 6:
                        EDID_Filldata("Stereo", "4-Way Interleaved", gridView);
                        break;

                    case 7:
                        EDID_Filldata("Stereo", "Side-by-Side Interleaved", gridView);
                        break;
                }

                stringBuilder.Clear();
                if ((data[17] & 0x10) == 0)
                {
                    if ((data[17] & 0x08) == 0)
                    {
                        EDID_Filldata("Syncs", "Analog Composite", gridView);
                    }
                    else
                    {
                        EDID_Filldata("Syncs", "Analog Bipolar Composite", gridView);
                    }

                    stringBuilder.Append("Serrate");
                    stringBuilder.Append(((data[17] & 0x04) == 0) ? " Off" : " On");
                    stringBuilder.Append(", Sync");
                    stringBuilder.Append(((data[17] & 0x02) == 0) ? " Green" : " RGB");
                }
                else
                {
                    if ((data[17] & 0x08) == 0)
                    {
                        EDID_Filldata("Syncs", "Digital Composite", gridView);

                        stringBuilder.Append("Serrate");
                        stringBuilder.Append(((data[17] & 0x04) == 0) ? " Off" : " On");
                        stringBuilder.Append(", ");
                        stringBuilder.Append(((data[17] & 0x02) == 0) ? "-hsync" : "+hsync");
                    }
                    else
                    {
                        EDID_Filldata("Syncs", "Digital Separate", gridView);

                        stringBuilder.Append(((data[17] & 0x04) == 0) ? "-vsync" : "+vsync");
                        stringBuilder.Append(", ");
                        stringBuilder.Append(((data[17] & 0x02) == 0) ? "-hsync" : "+hsync");
                    }
                }
                EDID_Filldata("Sync Profile", stringBuilder.ToString(), gridView);
                stringBuilder.Clear();
            }
        }

        private void Audio_ExCode(byte[] data, DataGridView gridView)
        {
            string code_string;
            var audio_code = (data[2] & 0xF8) >> 3;

            switch (audio_code)
            {
                case 0:
                    code_string = "Refer to Audio Coding Type (CT) field in Data Byte 1";
                    break;

                case 1:
                case 2:
                case 3:
                    code_string = "Unused";
                    break;

                case 4:
                    code_string = "MPEG-4 HE AAC";
                    break;

                case 5:
                    code_string = "MPEG-4 HE AAC v2";
                    break;

                case 6:
                    code_string = "MPEG-4 AAC LC";
                    break;

                case 7:
                    code_string = "DRA";
                    break;

                case 8:
                    code_string = "MPEG-4 HE AAC + MPEG Surround";
                    break;

                case 10:
                    code_string = "MPEG-4 AAC LC + MPEG Surround";
                    break;

                case 11:
                    code_string = "MPEG-H 3D Audio";
                    break;

                case 12:
                    code_string = "AC-4";
                    break;

                case 13:
                    code_string = "L-PCM 3D Audio";
                    break;

                default:
                    code_string = "Reserved";
                    break;
            }
            EDID_Filldata("Audio Format", code_string, gridView);
        }

        private void Audio_Code(int code, DataGridView gridView)
        {
            string code_string = null;

            switch (code)
            {
                case 0:
                    code_string = "Refer to Stream Header";
                    break;

                case 1:
                    code_string = "LPCM (IEC 60958 PCM [30, 31])";
                    break;

                case 2:
                    code_string = "Dolby Digital";
                    break;

                case 3:
                    code_string = "MPEG1 (Layer 1 & 2)";
                    break;

                case 4:
                    code_string = "MP3 (MPEG1 Layer 3)";
                    break;

                case 5:
                    code_string = "MPEG2 (MultiChannel)";
                    break;

                case 6:
                    code_string = "AAC LC";
                    break;

                case 7:
                    code_string = "DTS";
                    break;

                case 8:
                    code_string = "ATRAC";
                    break;

                case 9:
                    code_string = "One Bit Audio (DSD)";
                    break;

                case 10:
                    code_string = "Dolby Digital Plus";
                    break;

                case 11:
                    code_string = "DTS-HD";
                    break;

                case 12:
                    code_string = "Dolby TrueHD/MAT";
                    break;

                case 13:
                    code_string = "DST";
                    break;

                case 14:
                    code_string = "WMA Pro";
                    break;
            }
            EDID_Filldata("Audio Format", code_string, gridView);
        }

        private void Audio_ExChannel(byte[] data, DataGridView gridView)
        {
            string ch_string;
            int channel_num = 0;
            var audio_code = (data[2] & 0xF8) >> 3;

            if ((audio_code >= 4 && audio_code <= 6) || audio_code == 8 || audio_code == 10)
            {
                channel_num = (data[0] & 0x07) + 1;
            }
            else
            if (audio_code == 13)
            {
                channel_num = data[0] & 0x07;
                channel_num |= (data[1] & 0x80) >> 4;
                channel_num |= (data[2] & 0x80) >> 3;
                channel_num += 1;
            }

            if (channel_num > 0)
            {
                ch_string = Convert.ToDecimal(channel_num).ToString();
                EDID_Filldata(null, "Max Channel: " + ch_string, gridView);
            }
        }

        private void Audio_Channel(byte[] data, DataGridView gridView)
        {
            string ch_string;
            var audio_code = (data[0] & 0x78) >> 3;

            if (audio_code == 0)
            {
                ch_string = "Refer to Stream Header";
            }
            else
            {
                var channel_num = (data[0] & 0x07) + 1;
                ch_string = Convert.ToDecimal(channel_num).ToString();
            }
            EDID_Filldata(null, "Max Channel: " + ch_string, gridView);
        }

        private void Audio_ExSampleRate(byte[] data, DataGridView gridView)
        {
            var sample = 0;
            var audio_code = (data[2] & 0xF8) >> 3;
            StringBuilder stringBuilder = new StringBuilder();

            if ((audio_code >= 4 && audio_code <= 6) || audio_code == 8 || audio_code == 10)
            {
                sample = data[1] & 0x1F;
            }
            else
            if (audio_code == 11 || audio_code == 13)
            {
                sample = data[1] & 0x7F;
            }
            else
            if (audio_code == 12)
            {
                sample = data[1] & 0x56;
            }

            if ((audio_code >= 4 && audio_code <= 6) || audio_code == 8 || (audio_code >= 10 && audio_code <= 13))
            {
                if ((sample & 0x40) > 0) stringBuilder.Append("-192");
                if ((sample & 0x20) > 0) stringBuilder.Append("-176.4");
                if ((sample & 0x10) > 0) stringBuilder.Append("-96");
                if ((sample & 0x08) > 0) stringBuilder.Append("-88.2");
                if ((sample & 0x04) > 0) stringBuilder.Append("-48");
                if ((sample & 0x02) > 0) stringBuilder.Append("-44.1");
                if ((sample & 0x01) > 0) stringBuilder.Append("-32");

                EDID_Filldata(null, "Sample Rate: " + stringBuilder.ToString(), gridView);
            }
        }

        private void Audio_SampleRate(byte[] data, DataGridView gridView)
        {
            var sample = data[1] & 0x7F;
            StringBuilder stringBuilder = new StringBuilder();

            if (sample == 0) stringBuilder.Append("Refer to Stream Header");
            else
            {
                if ((sample & 0x40) > 0) stringBuilder.Append("-192");
                if ((sample & 0x20) > 0) stringBuilder.Append("-176.4");
                if ((sample & 0x10) > 0) stringBuilder.Append("-96");
                if ((sample & 0x08) > 0) stringBuilder.Append("-88.2");
                if ((sample & 0x04) > 0) stringBuilder.Append("-48");
                if ((sample & 0x02) > 0) stringBuilder.Append("-44.1");
                if ((sample & 0x01) > 0) stringBuilder.Append("-32");
            }
            EDID_Filldata(null, "Sample Rate: " + stringBuilder.ToString(), gridView);
        }

        private void Audio_ExBitrate(byte[] data, DataGridView gridView)
        {
            int bitrate;
            var audio_code = (data[2] & 0xF8) >> 3;
            StringBuilder stringBuilder = new StringBuilder();

            if (audio_code == 13)
            {
                bitrate = data[2] & 0x07;
                
                if (bitrate == 0) stringBuilder.Append("Refer to Stream Header");
                else
                {
                    if ((bitrate & 0x04) > 0) stringBuilder.Append("-24");
                    if ((bitrate & 0x02) > 0) stringBuilder.Append("-20");
                    if ((bitrate & 0x01) > 0) stringBuilder.Append("-16");
                }
                EDID_Filldata(null, "Bitrate: " + stringBuilder.ToString(), gridView);
            }

            if ((audio_code >= 4 && audio_code <= 6) || audio_code == 8 || audio_code == 10)
            {
                EDID_Filldata(null, "AAC frame 960 samples: " + (((data[2] & 0x02) == 0) ? "N" : "Y"), gridView);
                EDID_Filldata(null, "AAC frame 1024 samples: " + (((data[2] & 0x04) == 0) ? "N" : "Y"), gridView);

                if (audio_code == 6)
                {
                    EDID_Filldata(null, "Rec. ITU-R BS.2051 [139] System H 22.2 multichannel sound: " + (((data[2] & 0x01) == 0) ? "N" : "Y"), gridView);
                }
                else
                if (audio_code == 8 || audio_code == 10)
                {
                    EDID_Filldata(null, "Sink supports implicitly%s signaled MPEG Surround data" + (((data[2] & 0x01) == 0) ? "" : " and explicitly"), gridView);
                }
            }
            else
            if (audio_code == 11)
            {
                int audio_level = data[0] & 0x07;

                if (audio_level == 0)
                    EDID_Filldata(null, "3DA Level: Unspecified", gridView);
                else
                if (audio_level == 6 || audio_level == 7)
                {
                    EDID_Filldata(null, "3DA Level: Reserved", gridView);
                }
                else
                {
                    EDID_Filldata(null, "3DA Level: " + Convert.ToDecimal(audio_level).ToString(), gridView);
                }

                EDID_Filldata(null, "Baseline Profile: " + (((data[2] & 0x02) == 0) ? "N" : "Y"), gridView);
                EDID_Filldata(null, "Low Complexity Profile: " + (((data[2] & 0x01) == 0) ? "N" : "Y"), gridView);
            }
            else
            if (audio_code == 12 || audio_code == 14)
            {
                EDID_Filldata(null, "dependent value: " + Convert.ToDecimal(data[2] & 0x07).ToString(), gridView);
            }
        }

        private void Audio_Bitrate(byte[] data, DataGridView gridView)
        {
            int bitrate;
            var audio_code = (data[0] & 0x78) >> 3;
            StringBuilder stringBuilder = new StringBuilder();

            if (audio_code == 1)
            {
                bitrate = data[2] & 0x07;
                if (bitrate == 0) stringBuilder.Append("Refer to Stream Header");
                else
                {
                    if ((bitrate & 0x04) > 0) stringBuilder.Append("-24");
                    if ((bitrate & 0x02) > 0) stringBuilder.Append("-20");
                    if ((bitrate & 0x01) > 0) stringBuilder.Append("-16");
                }
                EDID_Filldata(null, "Bitrate: " + stringBuilder.ToString(), gridView);
            }
            else
            if (audio_code >= 2 && audio_code <= 8)
            {
                stringBuilder.Append(Convert.ToDecimal(data[2] * 8).ToString() + " kbps");
                EDID_Filldata(null, "Max Bitrate: " + stringBuilder.ToString(), gridView);
            }
            else
            if (audio_code >= 9 && audio_code <= 13)
            {
                if (audio_code == 10)
                {
                    EDID_Filldata(null, "ACMOD 28 Support: " + (((data[2] & 0x02) == 0) ? "N" : "Y"), gridView);
                    EDID_Filldata(null, "ATMOS Support: " + (((data[2] & 0x01) == 0) ? "N" : "Y"), gridView);
                }
                else
                if (audio_code == 12)
                {
                    EDID_Filldata(null, "MAT: " + (((data[2] & 0x02) == 0) ? "2.x" : "1.x"), gridView);
                    EDID_Filldata(null, "Decoder: " + (((data[2] & 0x01) == 0) ? "TrueHD/PCM/metadata" : "TrueHD"), gridView);
                }
                else
                    EDID_Filldata(null, "dependent value: " + Convert.ToDecimal(data[2]).ToString(), gridView);
            }
            else
            if (audio_code == 14)
            {
                stringBuilder.Append(Convert.ToDecimal(data[2] & 0x07).ToString());
                EDID_Filldata(null, "Profile: " + stringBuilder.ToString(), gridView);
            }
        }

        private void Audio_Byte(byte[] data, DataGridView gridView)
        {
            var audio_code = (data[0] & 0x78) >> 3;

            if (audio_code < 15)
            {
                Audio_Code(audio_code, gridView);
                Audio_Channel(data, gridView);
                Audio_SampleRate(data, gridView);
                Audio_Bitrate(data, gridView);
            }
            else
            {
                Audio_ExCode(data, gridView);
                Audio_ExChannel(data, gridView);
                Audio_ExSampleRate(data, gridView);
                Audio_ExBitrate(data, gridView);
            }
        }

        private void Audio_Data(byte[] data, DataGridView gridView)
        {
            byte[] audio_data;
            var audio_num = data[0] & 0x1F;

            for (int i = 0; i < audio_num / 3; i++)
            {
                audio_data = data.Skip(1 + i * 3).Take(3).ToArray();
                Audio_Byte(audio_data, gridView);
            }
        }

        private void Video_Data(byte[] data, DataGridView gridView)
        {
            var video_num = data[0] & 0x1F;
            StringBuilder stringBuilder = new StringBuilder();

            for (int i = 0; i < video_num; i++)
            {
                stringBuilder.Clear();
                stringBuilder.Append(Convert.ToDecimal(data[i + 1] & 0x7F).ToString() + ":");
                if ((data[i + 1] & 0x7F) > 64)
                {
                    stringBuilder.Append(Vic_List[data[i + 1] & 0x7F]);
                }
                else
                {
                    stringBuilder.Append(Vic_List[data[i + 1] & 0x7F]);
                    if ((data[i + 1] & 0x80) > 0)
                        stringBuilder.Append(", Native");
                }
                EDID_Filldata("VIC " + Convert.ToDecimal(i).ToString(), stringBuilder.ToString(), gridView);
            }
        }

        //refer: HDMI Spec 1.4/Table 8-13/8-15
        private void Vendor_Specific_HDMI_LLC(byte[] data, DataGridView gridView)
        {
            int size = data[0] & 0x1F;
            int temp = 0, stop = 0, latency = 0, index = 0;
            StringBuilder stringBuilder = new StringBuilder();

            for (int i = 0; i < size; i++)
            {
                int value = data[i + 1];

                switch (i)
                {
                    case 0://byte1,2,3
                    case 1:
                    case 2:
                        break;

                    case 3://byte4 AB
                        stringBuilder.Clear();
                        stringBuilder.Append(data[3].ToString("X2"));
                        stringBuilder.Append(data[2].ToString("X2"));
                        stringBuilder.Append(data[1].ToString("X2"));
                        EDID_Filldata("IEEE OUI", stringBuilder.ToString(), gridView);
                        temp = value;
                        break;

                    case 4://byte5 CD
                        stringBuilder.Clear();
                        stringBuilder.Append(Convert.ToDecimal((temp & 0xF0) >> 4).ToString());
                        stringBuilder.Append(".");
                        stringBuilder.Append(Convert.ToDecimal(temp & 0x0F).ToString());
                        stringBuilder.Append(".");
                        stringBuilder.Append(Convert.ToDecimal((value & 0xF0) >> 4).ToString());
                        stringBuilder.Append(".");
                        stringBuilder.Append(Convert.ToDecimal(value & 0x0F).ToString());
                        EDID_Filldata("Source Physical Address", stringBuilder.ToString(), gridView);
                        temp = 0;
                        break;

                    case 5://byte6
                        EDID_Filldata("Supports ACP,ISRC1/ISRC2", ((value & 0x80) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("Supports 48 bits/pixel(16 bits/color)", ((value & 0x40) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("Supports 36 bits/pixel(12 bits/color)", ((value & 0x20) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("Supports 30 bits/pixel(10 bits/color)", ((value & 0x10) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("Supports YCbCr 4:4:4 in deep color modes", ((value & 0x08) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("Supports DVI dual-link operation", ((value & 0x01) == 0) ? "N" : "Y", gridView);
                        break;

                    case 6://byte7
                        EDID_Filldata("Max TMDS Clock rate", Convert.ToDecimal(value * 5).ToString() + " MHz", gridView);
                        break;

                    case 7://byte8
                        latency = value & 0xC0;

                        EDID_Filldata("Latency_Fields_Present", ((value & 0x80) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("I_Latency_Fields_Present", ((value & 0x40) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("HDMI_Video_Present", ((value & 0x20) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("CNC3-Game Supported", ((value & 0x08) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("CNC2-Cinema Supported", ((value & 0x04) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("CNC1-Photo Supported", ((value & 0x02) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("CNC0-Graphics(text) Supported", ((value & 0x01) == 0) ? "N" : "Y", gridView);
                        break;

                    case 8://byte9
                        if ((latency & 0x80) == 0)
                        {
                            EDID_Filldata("3D_present", ((value & 0x80) == 0) ? "N" : "Y", gridView);
                            EDID_Filldata("3D_Multi_present", ((value & 0x60) == 0) ? "N" : "Y", gridView);
                        }
                        else
                        {
                            EDID_Filldata("Video_Latency", Convert.ToDecimal(value).ToString(), gridView);
                        }
                        break;

                    case 9://byte10
                        if ((latency & 0x80) == 0)
                        {
                            stop = 1;
                            index = i;
                            temp = (value & 0xE0) >> 5;
                            EDID_Filldata("HDMI_VIC_LEN", Convert.ToDecimal(temp).ToString(), gridView);
                            EDID_Filldata("HDMI_3D_LEN", Convert.ToDecimal(value & 0x1F).ToString(), gridView);
                        }
                        else
                        {
                            EDID_Filldata("Audio_Latency", Convert.ToDecimal(value).ToString(), gridView);
                        }
                        break;

                    case 10://byte11
                        if ((latency & 0x40) > 0)
                            EDID_Filldata("Interlaced_Video_Latency", Convert.ToDecimal(value).ToString(), gridView);
                        break;

                    case 11://byte12
                        if ((latency & 0x40) > 0)
                            EDID_Filldata("Interlaced_Audio_Latency", Convert.ToDecimal(value).ToString(), gridView);
                        break;

                    case 12://byte13
                        if ((latency & 0x80) > 0 && (latency & 0x40) > 0)
                        {
                            EDID_Filldata("3D_present", ((value & 0x80) == 0) ? "N" : "Y", gridView);
                            EDID_Filldata("3D_Multi_present", ((value & 0x60) == 0) ? "N" : "Y", gridView);
                        }
                        break;

                    case 13://byte14
                        if ((latency & 0x80) == 1 && (latency & 0x40) == 1)
                        {
                            stop = 1;
                            index = i;
                            temp = (value & 0xE0) >> 5;
                            EDID_Filldata("HDMI_VIC_LEN", Convert.ToDecimal(temp).ToString(), gridView);
                            EDID_Filldata("HDMI_3D_LEN", Convert.ToDecimal(value & 0x1F).ToString(), gridView);
                        }
                        break;

                    default:
                        stop = 1;
                        break;
                }

                if (stop == 1) break;
            }

            if (temp > 0)
            {
                for (int i = 0; i < temp; i++)
                {
                    int vic = data[i + index + 2];
                    EDID_Filldata("HDMI_VIC " + Convert.ToDecimal(vic).ToString(), Vic_4K[vic], gridView);
                }
            }
        }

        //refer: HDMI Spec 2.1/Table 10-6
        private void Vendor_Specific_HDMI_Forum(byte[] data, DataGridView gridView)
        {
            int temp = 0, stop = 0;
            int size = data[0] & 0x1F;
            StringBuilder stringBuilder = new StringBuilder();

            for (int i = 0; i < size; i++)
            {
                int value = data[i + 1];

                switch (i)
                {
                    case 0://byte1,2,3
                    case 1:
                    case 2:
                        break;

                    case 3://byte4
                        stringBuilder.Clear();
                        stringBuilder.Append(data[3].ToString("X2"));
                        stringBuilder.Append(data[2].ToString("X2"));
                        stringBuilder.Append(data[1].ToString("X2"));
                        EDID_Filldata("IEEE OUI", stringBuilder.ToString(), gridView);
                        EDID_Filldata("Version", Convert.ToDecimal(value).ToString(), gridView);
                        break;

                    case 4://byte5
                        EDID_Filldata("Max_TMDS_Character_Rate", Convert.ToDecimal(value * 5).ToString() + " MHz", gridView);
                        break;

                    case 5://byte6
                        EDID_Filldata("SCDC_Present", ((value & 0x80) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("RR_Capable", ((value & 0x40) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("CCBPCI", ((value & 0x10) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("LTE_340Mcsc_scramble", ((value & 0x08) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("Independent_View", ((value & 0x04) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("Dual_View", ((value & 0x02) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("3D_OSD_Disparity", ((value & 0x01) == 0) ? "N" : "Y", gridView);
                        break;

                    case 6://byte7
                        stringBuilder.Clear();
                        switch ((value & 0xF0) >> 4)
                        {
                            case 0:
                                stringBuilder.Append("Not supported");
                                break;

                            case 1:
                                stringBuilder.Append("3 Gbps @ 3 Lanes");
                                break;

                            case 2:
                                stringBuilder.Append("3/6 Gbps @ 3 Lanes");
                                break;

                            case 3:
                                stringBuilder.Append("3/6 Gbps @ 3 Lanes, 6G @ 4 Lanes");
                                break;

                            case 4:
                                stringBuilder.Append("3/6 Gbps @ 3 Lanes, 6/8 Gbps @ 4 Lanes");
                                break;

                            case 5:
                                stringBuilder.Append("3/6 Gbps @ 3 Lanes, 6/8/10 Gbps @ 4 Lanes");
                                break;

                            case 6:
                                stringBuilder.Append("3/6 Gbps @ 3 Lanes, 6/8/10/12 Gbps @ 4 Lanes");
                                break;

                            case 7:
                            case 8:
                            case 9:
                            case 10:
                            case 11:
                            case 12:
                            case 13:
                            case 14:
                            case 15:
                                stringBuilder.Append("Reserved");
                                break;
                        }
                        EDID_Filldata("Max_FRL_Rate", stringBuilder.ToString(), gridView);
                        EDID_Filldata("DC_48bit_420", ((value & 0x04) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("DC_36bit_420", ((value & 0x02) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("DC_30bit_420", ((value & 0x01) == 0) ? "N" : "Y", gridView);
                        break;

                    case 7://byte8
                        EDID_Filldata("Mdelta", ((value & 0x20) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("CinemaVRR", ((value & 0x10) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("CNMVRR", ((value & 0x08) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("FVA", ((value & 0x04) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("ALLM", ((value & 0x02) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("FAPA_start_location", ((value & 0x01) == 0) ? "N" : "Y", gridView);
                        break;

                    case 8://byte9
                        temp = value & 0xC0;
                        EDID_Filldata("VRRmin", Convert.ToDecimal(value & 0x3F).ToString(), gridView);
                        break;

                    case 9://byte10
                        EDID_Filldata("VRRmax", Convert.ToDecimal(value | (temp << 2)).ToString(), gridView);
                        break;

                    case 10://byte11
                        EDID_Filldata("DSC_1p2", ((value & 0x80) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("DSC_Native_420", ((value & 0x40) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("DSC_All_bpp", ((value & 0x08) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("DSC_16bpc", ((value & 0x04) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("DSC_12bpc", ((value & 0x02) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("DSC_10bpc", ((value & 0x01) == 0) ? "N" : "Y", gridView);
                        break;

                    case 11://byte12
                        EDID_Filldata("DSC_Max_FRL_Rate", Convert.ToDecimal((value & 0xF0) >> 4).ToString(), gridView);
                        EDID_Filldata("DSC_MaxSlices", Convert.ToDecimal(value & 0x0F).ToString(), gridView);
                        break;

                    case 12://byte13
                        EDID_Filldata("DSC_TotalChunkKBytes", Convert.ToDecimal(value & 0x3F).ToString(), gridView);
                        break;

                    default:
                        stop = 1;
                        break;
                }

                if (stop > 0) break;
            }
        }

        private void Vendor_Specific_General(byte[] data, DataGridView gridView)
        {
            int size = (data[0] & 0x1F) - 3;
            StringBuilder stringBuilder = new StringBuilder();
            byte[] db_data = data.Skip(4).Take(size).ToArray();

            stringBuilder.Clear();
            stringBuilder.Append(data[3].ToString("X2"));
            stringBuilder.Append(data[2].ToString("X2"));
            stringBuilder.Append(data[1].ToString("X2"));
            EDID_Filldata("IEEE OUI", stringBuilder.ToString(), gridView);

            stringBuilder.Clear();
            foreach (byte b in db_data)
            {
                stringBuilder.Append(b.ToString("X2") + ' ');
            }
            EDID_Filldata("Payload", stringBuilder.ToString(), gridView);
        }

        //refer: CTA-861-H table-76
        private void Speaker_Allocation(byte[] data, DataGridView gridView)
        {
            EDID_Filldata("Front Left/Front Right(FL/FR)", ((data[1] & 0x01) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Low Frequency Effects 1(LFE1)", ((data[1] & 0x02) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Front Center(FC)", ((data[1] & 0x04) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Back Left/Back Right(BL/BR)", ((data[1] & 0x08) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Back Center(BC)", ((data[1] & 0x10) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Front Left Center/Front Right Center(FLC/FRC)", ((data[1] & 0x20) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Rear Left Center/Rear Right Center(RLC/RRC)", ((data[1] & 0x40) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Front Left Wide/Front Right Wide(FLW/FRW)", ((data[1] & 0x80) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Top Front Left/Top Front Right(TpFL/TpFR)", ((data[2] & 0x01) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Top Center(TpC)", ((data[2] & 0x02) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Top Front Center(TpFC)", ((data[2] & 0x04) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Left/Right Surround(LS/RS)", ((data[2] & 0x08) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Low Frequency Effects 2(LFE2)", ((data[2] & 0x10) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Top Back Center(TpBC)", ((data[2] & 0x20) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Side Left/Right(SiL/SiR)", ((data[2] & 0x40) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Top Side Left/Right(TpSiL/TpSiR)", ((data[2] & 0x80) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Top Back Left/Right(TpBL/TpBR)", ((data[3] & 0x01) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Bottom Front Center(BtFC)", ((data[3] & 0x02) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Bottom Front Left/Right(BtFL/BtFR)", ((data[3] & 0x04) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("Top Left/Right Surround(TpLS/TpRS)", ((data[3] & 0x08) == 0) ? "N" : "Y", gridView);
        }

        //refer: CTA-861-H table-81
        private void Video_Capability(byte[] data, DataGridView gridView)
        {
            int temp;
            StringBuilder stringBuilder = new StringBuilder();

            EDID_Filldata("Quantization Range(QY-YCC)", ((data[2] & 0x80) == 0) ? "No Data" : "selectable(via AVI YQ)", gridView);
            EDID_Filldata("Quantization Range(QS-RGB)", ((data[2] & 0x40) == 0) ? "No Data" : "selectable(via AVI Q)", gridView);

            stringBuilder.Clear();
            temp = (data[2] & 0x30) >> 4;
            switch (temp)
            {
                case 0:
                    stringBuilder.Append("No Data (refer to S_CE/S_IT fields)");
                    break;

                case 1:
                    stringBuilder.Append("Always Overscanned");
                    break;

                case 2:
                    stringBuilder.Append("Always Underscanned");
                    break;

                case 3:
                    stringBuilder.Append("Supports both Over and Under scan");
                    break;
            }
            EDID_Filldata("PT Overscan/underscan behavior(S_PT10)", stringBuilder.ToString(), gridView);

            stringBuilder.Clear();
            temp = (data[2] & 0x0C) >> 2;
            switch (temp)
            {
                case 0:
                    stringBuilder.Append("IT Video Formats not supported");
                    break;

                case 1:
                    stringBuilder.Append("Always Overscanned");
                    break;

                case 2:
                    stringBuilder.Append("Always Underscanned");
                    break;

                case 3:
                    stringBuilder.Append("Supports both Over and Under scan");
                    break;
            }
            EDID_Filldata("IT Overscan/underscan behavior(S_IT10)", stringBuilder.ToString(), gridView);

            stringBuilder.Clear();
            temp = data[2] & 0x03;
            switch (temp)
            {
                case 0:
                    stringBuilder.Append("CE Video Formats not supported");
                    break;

                case 1:
                    stringBuilder.Append("Always Overscanned");
                    break;

                case 2:
                    stringBuilder.Append("Always Underscanned");
                    break;

                case 3:
                    stringBuilder.Append("Supports both Over and Under scan");
                    break;
            }
            EDID_Filldata("CE Overscan/underscan behavior(S_CE10)", stringBuilder.ToString(), gridView);
        }

        //refer: Dolby Vision HDMI Transmission spec v2.9
        //version 0: 25bytes
        //version 1: 15bytes
        //version 1: 12bytes low latency
        //version 2: 12bytes
        private void Dolby_Vendor_Specific_Video_v0(byte[] data, int size, DataGridView gridView)
        {
            //for (int i = 0; i < size; i++)
            //{
            //    int value = data[i + 5];

            //    switch (i)
            //    {
            //        case 0://byte 5
            //            if (version == 0)
            //                EDID_Filldata("Supports_global_dimming", ((value & 0x04) == 0) ? "N" : "Y", gridView);
            //            else
            //            if (version == 1 || version == 2)
            //            {
            //                int dm_version = (value & 0x1C) >> 2;
            //                switch (dm_version)
            //                {
            //                    case 0:
            //                        EDID_Filldata("DM_Version", "v2.x", gridView);
            //                        break;

            //                    case 1:
            //                        EDID_Filldata("DM_Version", "v3.x", gridView);
            //                        break;

            //                    case 2:
            //                        EDID_Filldata("DM_Version", "v4.x", gridView);
            //                        break;

            //                    case 3:
            //                        EDID_Filldata("DM_Version", "v4.x with VS-EMDS", gridView);
            //                        break;

            //                    default:
            //                        EDID_Filldata("DM_Version", "reserved", gridView);
            //                        break;
            //                }
            //            }

            //            if (version == 0 || version == 1)
            //            {
            //                EDID_Filldata("Supports_2160p60Hz", ((value & 0x02) == 0) ? "N" : "Y", gridView);
            //            }
            //            else
            //            if (version == 2)
            //            {
            //                EDID_Filldata("Supports_Backlight_Control", ((value & 0x02) == 0) ? "N" : "Y", gridView);
            //            }

            //            EDID_Filldata("Supports_YUV422_12bit", ((value & 0x01) == 0) ? "N" : "Y", gridView);
            //            break;

            //        case 1://byte 6
            //            if (version == 0)
            //            {
            //                EDID_Filldata("Supports_YUV422_12bit", ((value & 0x01) == 0) ? "N" : "Y", gridView);
            //            }
            //            else
            //            if (version == 1)
            //            {
            //                EDID_Filldata("Target_max_luminance", Convert.ToDouble(((value & 0xFE) >> 1) * 50.0f + 100.0f).ToString("0.0000000"), gridView);
            //                EDID_Filldata("Supports_global_dimming", ((value & 0x01) == 0) ? "N" : "Y", gridView);
            //            }
            //            else
            //            if (version == 2)
            //            {
            //                EDID_Filldata("Target_min_PQ_v2", Convert.ToDouble(((value & 0xF8) >> 3) * 20.0f).ToString("0.0000000"), gridView);
            //                EDID_Filldata("Supports_global_dimming", ((value & 0x01) == 0) ? "N" : "Y", gridView);
            //                EDID_Filldata("BackLt_Min_Luma", Convert.ToDecimal((value & 0x03 + 1) * 25).ToString(), gridView);
            //            }
            //            break;

            //        case 2://byte 7
            //            break;

            //        case 3://byte 8
            //            break;

            //        case 4://byte 9
            //            break;

            //        case 5://byte 10
            //            break;

            //        case 6://byte 11
            //            break;

            //        case 7://byte 12
            //            break;

            //        case 8://byte 13
            //            break;

            //        case 9://byte 14
            //            break;

            //        case 16://byte 21
            //            break;

            //        default:
            //            stop = 1;
            //            break;
            //    }

            //    if (stop > 0) break;
            //}
        }

        private void Dolby_Vendor_Specific_Video_v1(byte[] data, int size, DataGridView gridView)
        {
            //for (int i = 0; i < size; i++)
            //{
            //    int value = data[i + 5];

            //    switch (i)
            //    {
            //        case 0://byte 5
            //            if (version == 0)
            //                EDID_Filldata("Supports_global_dimming", ((value & 0x04) == 0) ? "N" : "Y", gridView);
            //            else
            //            if (version == 1 || version == 2)
            //            {
            //                int dm_version = (value & 0x1C) >> 2;
            //                switch (dm_version)
            //                {
            //                    case 0:
            //                        EDID_Filldata("DM_Version", "v2.x", gridView);
            //                        break;

            //                    case 1:
            //                        EDID_Filldata("DM_Version", "v3.x", gridView);
            //                        break;

            //                    case 2:
            //                        EDID_Filldata("DM_Version", "v4.x", gridView);
            //                        break;

            //                    case 3:
            //                        EDID_Filldata("DM_Version", "v4.x with VS-EMDS", gridView);
            //                        break;

            //                    default:
            //                        EDID_Filldata("DM_Version", "reserved", gridView);
            //                        break;
            //                }
            //            }

            //            if (version == 0 || version == 1)
            //            {
            //                EDID_Filldata("Supports_2160p60Hz", ((value & 0x02) == 0) ? "N" : "Y", gridView);
            //            }
            //            else
            //            if (version == 2)
            //            {
            //                EDID_Filldata("Supports_Backlight_Control", ((value & 0x02) == 0) ? "N" : "Y", gridView);
            //            }

            //            EDID_Filldata("Supports_YUV422_12bit", ((value & 0x01) == 0) ? "N" : "Y", gridView);
            //            break;

            //        case 1://byte 6
            //            if (version == 0)
            //            {
            //                EDID_Filldata("Supports_YUV422_12bit", ((value & 0x01) == 0) ? "N" : "Y", gridView);
            //            }
            //            else
            //            if (version == 1)
            //            {
            //                EDID_Filldata("Target_max_luminance", Convert.ToDouble(((value & 0xFE) >> 1) * 50.0f + 100.0f).ToString("0.0000000"), gridView);
            //                EDID_Filldata("Supports_global_dimming", ((value & 0x01) == 0) ? "N" : "Y", gridView);
            //            }
            //            else
            //            if (version == 2)
            //            {
            //                EDID_Filldata("Target_min_PQ_v2", Convert.ToDouble(((value & 0xF8) >> 3) * 20.0f).ToString("0.0000000"), gridView);
            //                EDID_Filldata("Supports_global_dimming", ((value & 0x01) == 0) ? "N" : "Y", gridView);
            //                EDID_Filldata("BackLt_Min_Luma", Convert.ToDecimal((value & 0x03 + 1) * 25).ToString(), gridView);
            //            }
            //            break;

            //        case 2://byte 7
            //            break;

            //        case 3://byte 8
            //            break;

            //        case 4://byte 9
            //            break;

            //        case 5://byte 10
            //            break;

            //        case 6://byte 11
            //            break;

            //        case 7://byte 12
            //            break;

            //        case 8://byte 13
            //            break;

            //        case 9://byte 14
            //            break;

            //        case 16://byte 21
            //            break;

            //        default:
            //            stop = 1;
            //            break;
            //    }

            //    if (stop > 0) break;
            //}
        }

        private void Dolby_Vendor_Specific_Video_v2(byte[] data, int size, DataGridView gridView)
        {
            int stop = 0;

            for (int i = 0; i < size; i++)
            {
                int value = data[i];

                switch (i)
                {
                    case 0://byte 5
                        int dm_version = (value & 0x1C) >> 2;
                        switch (dm_version)
                        {
                            case 0:
                                EDID_Filldata("DM_Version", "v2.x", gridView);
                                break;

                            case 1:
                                EDID_Filldata("DM_Version", "v3.x", gridView);
                                break;

                            case 2:
                                EDID_Filldata("DM_Version", "v4.x", gridView);
                                break;

                            case 3:
                                EDID_Filldata("DM_Version", "v4.x with VS-EMDS", gridView);
                                break;

                            default:
                                EDID_Filldata("DM_Version", "reserved", gridView);
                                break;
                        }

                        EDID_Filldata("Supports_Backlight_Control", ((value & 0x02) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("Supports_YUV422_12bit", ((value & 0x01) == 0) ? "N" : "Y", gridView);
                        break;

                    case 1://byte 6
                        EDID_Filldata("Target_min_PQ_v2", Convert.ToDouble(((value & 0xF8) >> 3) * 20.0f).ToString("0.0000000"), gridView);
                        EDID_Filldata("Supports_global_dimming", ((value & 0x01) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("BackLt_Min_Luma", Convert.ToDecimal((value & 0x03 + 1) * 25).ToString(), gridView);
                        break;

                    case 2://byte 7
                        break;

                    case 3://byte 8
                        break;

                    case 4://byte 9
                        break;

                    case 5://byte 10
                        break;

                    case 6://byte 11
                        break;

                    case 7://byte 12
                        break;

                    default:
                        stop = 1;
                        break;
                }

                if (stop > 0) break;
            }
        }

        private void Dolby_Vendor_Specific_Video(byte[] data, DataGridView gridView)
        {
            int size = (data[0] & 0x1F) - 4;
            int version = (data[5] & 0xE0) >> 5;
            byte[] db_data = data.Skip(5).Take(size).ToArray();

            switch (version)
            {
                case 0:
                    Dolby_Vendor_Specific_Video_v0(db_data, size, gridView);
                    break;

                case 1:
                    Dolby_Vendor_Specific_Video_v1(db_data, size, gridView);
                    break;

                case 2:
                    Dolby_Vendor_Specific_Video_v2(db_data, size, gridView);
                    break;
            }
        }

        //refer: CTA-861-H table-78
        private void Colorimetry_Data(byte[] data, DataGridView gridView)
        {
            EDID_Filldata("BT.2020-RGB (ITU-R BT.2020 [40] RGB)", ((data[2] & 0x80) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("BT.2020-YCC (ITU-R BT.2020 [40] YCbCr)", ((data[2] & 0x40) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("BT.2020-cYCC (ITU-R BT.2020 [40] YcCbcCrc)", ((data[2] & 0x20) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("opRGB (IEC 61966-2-5 [32])", ((data[2] & 0x10) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("opYCC-601 (IEC 61966-2-5 [32], Annex A)", ((data[2] & 0x08) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("sYCC-601 (IEC 61966-2-1/Amendment 1 [34])", ((data[2] & 0x04) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("xvYCC-709 (IEC 61966-2-4 [5])", ((data[2] & 0x02) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("xvYCC-601 (IEC 61966-2-4 [5])", ((data[2] & 0x01) == 0) ? "N" : "Y", gridView);

            EDID_Filldata("ST2113RGB23 (SMPTE ST 2113 [49] RGB)", ((data[3] & 0x80) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("ICTCP (ITU-R BT.2100 [48] ICTCP)", ((data[3] & 0x40) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("F45 (Reserved)", ((data[3] & 0x20) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("F44 (Reserved)", ((data[3] & 0x10) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("MD3 (Future metadata profile)", ((data[3] & 0x08) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("MD2 (Future metadata profile)", ((data[3] & 0x04) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("MD1 (Future metadata profile)", ((data[3] & 0x02) == 0) ? "N" : "Y", gridView);
            EDID_Filldata("MD0 (Future metadata profile)", ((data[3] & 0x01) == 0) ? "N" : "Y", gridView);
        }

        //refer: CTA-861-H table-92
        private void HDR_Static_Metadata(byte[] data, DataGridView gridView)
        {
            int temp = 0;
            int size = (data[0] & 0x1F) - 1;
            StringBuilder stringBuilder = new StringBuilder();

            for (int i = 0; i < size; i++)
            {
                int value = data[i + 2];

                switch (i)
                {
                    case 0://byte3
                        EDID_Filldata("TF_5 (Reserved)", ((value & 0x20) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("TF_4 (Reserved)", ((value & 0x10) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("TF_3 (HLG based on Rec. ITU-R BT.2100)", ((value & 0x08) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("TF_2 (PQ based on SMPTE ST 2084)", ((value & 0x04) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("TF_1 (Traditional HDR)", ((value & 0x02) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("TF_0 (Traditional SDR)", ((value & 0x01) == 0) ? "N" : "Y", gridView);
                        break;

                    case 1://byte4
                        EDID_Filldata("SM_7 (Reserved)", ((value & 0x80) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("SM_6 (Reserved)", ((value & 0x40) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("SM_5 (Reserved)", ((value & 0x20) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("SM_4 (Reserved)", ((value & 0x10) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("SM_3 (Reserved)", ((value & 0x08) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("SM_2 (Reserved)", ((value & 0x04) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("SM_1 (Reserved)", ((value & 0x02) == 0) ? "N" : "Y", gridView);
                        EDID_Filldata("SM_0 (Static Metadata Type 1)", ((value & 0x01) == 0) ? "N" : "Y", gridView);
                        break;

                    case 2://byte5
                        temp = value;
                        stringBuilder.Clear();
                        stringBuilder.Append(Convert.ToDouble(50.0f * Math.Pow(2.0, value / 32.0f)).ToString("0.000"));
                        stringBuilder.Append(" cd/m^2");
                        EDID_Filldata("Max Luminance data", stringBuilder.ToString(), gridView);
                        break;

                    case 3://byte6
                        stringBuilder.Clear();
                        stringBuilder.Append(Convert.ToDouble(50.0f * Math.Pow(2.0, value / 32.0f)).ToString("0.000"));
                        stringBuilder.Append(" cd/m^2");
                        EDID_Filldata("Avg Luminance data", stringBuilder.ToString(), gridView);
                        break;

                    case 4://byte7
                        stringBuilder.Clear();
                        stringBuilder.Append(Convert.ToDouble(50.0f * Math.Pow(2.0, temp / 32.0f) * Math.Pow(value / 255.0f, 2.0f) / 100.0f).ToString("0.000"));
                        stringBuilder.Append(" cd/m^2");
                        EDID_Filldata("Min Luminance data", stringBuilder.ToString(), gridView);
                        break;

                    default:
                        stringBuilder.Clear();
                        stringBuilder.Append(value.ToString("X2"));
                        EDID_Filldata("Byte " + Convert.ToDecimal(i + 3).ToString(), stringBuilder.ToString(), gridView);
                        break;
                }
            }
        }

        //refer: CTA-861-H table-89
        private void YCbCr420_Video(byte[] data, DataGridView gridView)
        {
            var video_num = (data[0] & 0x1F) - 1;
            StringBuilder stringBuilder = new StringBuilder();

            for (int i = 0; i < video_num; i++)
            {
                stringBuilder.Clear();
                stringBuilder.Append(Convert.ToDecimal(data[i + 2]).ToString() + ":");
                stringBuilder.Append(Vic_List[data[i + 2]]);
                EDID_Filldata("VIC " + Convert.ToDecimal(i).ToString(), stringBuilder.ToString(), gridView);
            }
        }

        //refer: CTA-861-H table-90
        private void YCbCr420_Capability_Map(int block, byte[] edid, byte[] data, DataGridView gridView)
        {
            var size = (data[0] & 0x1F) - 1;
            StringBuilder stringBuilder = new StringBuilder();
            var vic_offset = Block_Offset(block, edid, 0x40, 132);
            var vic_count = edid[vic_offset] & 0x1F;

            for (int i = 0; i < size; i++)
            {
                for (int j = 0; j < 8; j++)
                {
                    if (vic_count > 0)
                    {
                        vic_count--;
                        stringBuilder.Clear();
                        stringBuilder.Append("Bit" + Convert.ToDecimal(j + i * 8).ToString() + ": ");
                        stringBuilder.Append("(" + Convert.ToDecimal(edid[vic_offset + j + i * 8 + 1] & 0x7F) + ")");
                        stringBuilder.Append(Vic_List[edid[vic_offset + j + i * 8 + 1] & 0x7F]);
                        EDID_Filldata(stringBuilder.ToString(), ((data[i + 2] & (1 << j)) > 0) ? "Y" : "N", gridView);
                    }
                }
            }
        }

        private void Vendor_Specific_Audio(byte[] data, DataGridView gridView)
        {
            int size = (data[0] & 0x1F) - 3;
            StringBuilder stringBuilder = new StringBuilder();
            byte[] db_data = data.Skip(4).Take(size).ToArray();

            stringBuilder.Clear();
            stringBuilder.Append(data[3].ToString("X2"));
            stringBuilder.Append(data[2].ToString("X2"));
            stringBuilder.Append(data[1].ToString("X2"));
            EDID_Filldata("IEEE OUI", stringBuilder.ToString(), gridView);

            stringBuilder.Clear();
            foreach (byte b in db_data)
            {
                stringBuilder.Append(b.ToString("X2") + ' ');
            }
            EDID_Filldata("Payload", stringBuilder.ToString(), gridView);
        }

        public void EDID_Parsedata(byte[] edid, TreeNode node, DataGridView gridView)
        {
            byte[] data;
            Blocks block = (Blocks)node.Tag;
            data = edid.Skip(block.Offset).Take(block.Size).ToArray();

            switch (node.Text)
            {
                case "Header":
                    Header_Info(block.Tag, data, gridView);
                    break;

                case "Vendor/Product Information":
                    Vendor_Information(data, gridView);
                    break;

                case "EDID Version/Revision":
                    EDID_Filldata("Version", Convert.ToDecimal(data[0]).ToString(), gridView);
                    EDID_Filldata("Revision", Convert.ToDecimal(data[1]).ToString(), gridView);
                    break;

                case "Video Input Definition":
                    Video_Input_Definition(data, gridView);
                    break;

                case "Hors/Vert Screen Size":
                    Hors_Vert_Screen_Size(data, gridView);
                    break;

                case "Display Transfer Characteristic":
                    Display_Transfer_Characteristic(data, gridView);
                    break;

                case "Feature Support":
                    Feature_Support(edid, data, gridView);
                    break;

                case "Color Characteristics":
                    Color_Characteristics(data, gridView);
                    break;

                case "Established Timings":
                    Established_Timing(data, gridView);
                    break;

                case "Standard Timing 1":
                case "Standard Timing 2":
                case "Standard Timing 3":
                case "Standard Timing 4":
                case "Standard Timing 5":
                case "Standard Timing 6":
                case "Standard Timing 7":
                case "Standard Timing 8":
                    Standard_Timing(data, gridView);
                    break;

                case "Detailed Timing/Descriptor 1":
                case "Detailed Timing/Descriptor 2":
                case "Detailed Timing/Descriptor 3":
                case "Detailed Timing/Descriptor 4":
                case "DTD 1":
                case "DTD 2":
                case "DTD 3":
                case "DTD 4":
                case "DTD 5":
                    Detailed_Timing_Descriptor((edid[18] & 0x02) == 0, data, gridView);
                    break;

                case "Extension Flag":
                    EDID_Filldata("Extension Flag", data[0].ToString("X2"), gridView);
                    break;

                case "Checksum":
                    EDID_Filldata("Checksum", data[0].ToString("X2"), gridView);
                    break;

                case "Audio Data":
                    Audio_Data(data, gridView);
                    break;

                case "Video Data":
                    Video_Data(data, gridView);
                    break;

                case "Vendor Specific HDMI 1.4b":
                    Vendor_Specific_HDMI_LLC(data, gridView);
                    break;

                case "Vendor Specific HDMI Forum":
                    Vendor_Specific_HDMI_Forum(data, gridView);
                    break;

                case "Vendor Specific General":
                    Vendor_Specific_General(data, gridView);
                    break;

                case "Speaker Allocation":
                    Speaker_Allocation(data, gridView);
                    break;

                case "Video Capability":
                    Video_Capability(data, gridView);
                    break;

                case "Dolby Vendor Specific Video":
                    Dolby_Vendor_Specific_Video(data, gridView);
                    break;

                case "HDR10+ Video":
                    break;

                case "Vendor Specific Video":
                    break;

                case "Colorimetry":
                    Colorimetry_Data(data, gridView);
                    break;

                case "HDR Static Metadata":
                    HDR_Static_Metadata(data, gridView);
                    break;

                case "HDR Dynamic Metadata":
                    break;

                case "Video Format Preference":
                    break;

                case "YCbCr4:2:0 Video":
                    YCbCr420_Video(data, gridView);
                    break;

                case "YCbCr4:2:0 Capability Map":
                    YCbCr420_Capability_Map(block.Num, edid, data, gridView);
                    break;

                case "Vendor-Specific Audio":
                    Vendor_Specific_Audio(data, gridView);
                    break;

                case "HDMI Audio":
                    break;

                case "Room Configuration":
                    break;

                case "Speaker Location":
                    break;
            }
        }
    }
}
