﻿﻿using System;
using System.Windows.Forms;

namespace EDID_Manager
{
    public static class LogHelper
    {
        private static TextBox _logTextBox;

        // 初始化日志控件
        public static void Initialize(TextBox logTextBox)
        {
            _logTextBox = logTextBox;
        }

        // 添加日志信息
        public static void Log(string message)
        {
            if (_logTextBox != null)
            {
                // 在UI线程上执行
                if (_logTextBox.InvokeRequired)
                {
                    _logTextBox.Invoke(new Action<string>(Log), message);
                    return;
                }

                // 添加时间戳
                string logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}";
                
                // 添加到文本框，确保新日志在最后
                _logTextBox.AppendText(logMessage + Environment.NewLine);
                
                // 滚动到最后一行
                _logTextBox.SelectionStart = _logTextBox.Text.Length;
                _logTextBox.ScrollToCaret();
            }
        }

        // 记录异常信息
        public static void LogError(string message, Exception ex = null)
        {
            string errorMessage = message;
            if (ex != null)
            {
                errorMessage += $" - {ex.Message}";
            }
            
            Log($"错误: {errorMessage}");
        }

        // 记录警告信息
        public static void LogWarning(string message)
        {
            Log($"警告: {message}");
        }

        // 记录信息性消息
        public static void LogInfo(string message)
        {
            Log($"信息: {message}");
        }

        // 记录成功信息
        public static void LogSuccess(string message)
        {
            Log($"成功: {message}");
        }
    }
}
