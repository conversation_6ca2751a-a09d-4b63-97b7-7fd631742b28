﻿using System;
using System.Windows.Forms;
using System.Runtime.InteropServices;

namespace EDID_Manager
{
    public partial class MsgBox : Form
    {
        public MsgBox()
        {
            InitializeComponent();
        }

        [DllImport("user32", EntryPoint = "HideCaret")]
        private static extern bool Hide<PERSON>aret(IntPtr hWnd);

        void TextBox1_GotFocus(object sender, EventArgs e)
        {
            HideCaret((sender as TextBox).Handle);
        }

        void TextBox1_MouseDown(object sender, MouseEventArgs e)
        {
            HideCaret((sender as TextBox).Handle);
        }

        private void MsgBox_Load(object sender, EventArgs e)
        {
            textBox1.GotFocus += TextBox1_GotFocus;
            textBox1.MouseDown += TextBox1_MouseDown;
        }

        public void Set_String(string text)
        {
            textBox1.Text = text;
        }

        private void Button1_Click(object sender, EventArgs e)
        {
            Close();
        }
    }
}
