﻿using Common;
using System.Windows.Forms;
using System.Collections.Generic;

namespace TreeBuild
{
    public class Trees
    {
        private int Tree_Find(List<TreeNode> list, Blocks block)
        {
            Blocks newblock;

            for (int i = 0; i < list.Count; i++)
            {
                newblock = (Blocks)list[i].Tag;

                if (newblock.Level == (block.Level - 1) && newblock.Directory && list[i].Text == block.Pos)
                    return i;
            }

            return -1;
        }

        private void Tree_Build(List<TreeNode> tree, TreeView treeView)
        {
            int root;
            Blocks newblock;

            for (int i = 0; i < tree.Count; i++)
            {
                newblock = (Blocks)tree[i].Tag;

                if (newblock.Level == 0)
                {
                    treeView.Nodes.Add(tree[i]);
                }
                else
                {
                    root = Tree_Find(tree, newblock);
                    tree[root].Nodes.Add(tree[i]);
                }
            }
        }

        private void Tree_Add(List<TreeNode> tree, string text, string key, int blockx, byte tag, int offset, int size, int level, bool directory, string posision)
        {
            Blocks block = new Blocks() { Num = blockx, Tag = tag, Offset = offset, Size = size, Level = level, Directory = directory, Pos = posision };
            tree.Add(new TreeNode() { Tag = block, Text = text, ImageKey = key, SelectedImageKey = key });
        }

        private int DTD_Count(int block, byte[] edid)
        {
            int dtd_pos = edid[block * 128 + 2] + block * 128;
            if (dtd_pos <= (block * 128)) return 0;

            int count = 0, offset;

            for (; ; )
            {
                offset = dtd_pos + count * 18;
                if (offset > 0xFC) break;

                if (edid[offset] == 0x00 && edid[offset + 1] == 0x00 && edid[offset + 2] == 0x00 && edid[offset + 3] == 0x00)
                    break;
                else
                    count++;
            }

            return count;
        }

        private void CTABlock_Tree(int block, byte[] edid, TreeView treeView)
        {
            int offset = block * 128;
            byte extension_tag = edid[offset];
            var ext_tree = new List<TreeNode>();
            int dtd_pos = edid[block * 128 + 2] + block * 128;

            Tree_Add(ext_tree, "CTA Block", "NODE_BLOCK", block, extension_tag, offset, 128, 0, true, null);
            Tree_Add(ext_tree, "Header", "ROOT_BLOCK", block, extension_tag, offset, 4, 1, false, "CTA Block");
            offset += 4;
            Tree_Add(ext_tree, "Data Blocks", "NODE_BLOCK", block, extension_tag, offset, 0, 1, true, "CTA Block");

            //data block tree
            int block_tag, block_length;
            string text = null, key = null;

            do
            {
                block_tag = (edid[offset] & 0xE0) >> 5;
                block_length = edid[offset] & 0x1F;

                switch (block_tag)
                {
                    case 0:
                    case 6:
                        text = "Reserved"; key = "ROOT_BLOCK";
                        break;

                    case 1:
                        text = "Audio Data"; key = "ROOT_BLOCK";
                        break;

                    case 2:
                        text = "Video Data"; key = "ROOT_BLOCK";
                        break;

                    case 3:
                        int ieee = edid[offset + 1] | (edid[offset + 2] << 8) | (edid[offset + 3] << 16);
                        switch (ieee)
                        {
                            case 0x000C03:
                                text = "Vendor Specific HDMI 1.4b"; key = "ROOT_BLOCK";
                                break;

                            case 0xC45DD8:
                                text = "Vendor Specific HDMI Forum"; key = "ROOT_BLOCK";
                                break;

                            default:
                                text = "Vendor Specific General"; key = "ROOT_BLOCK";
                                break;
                        }
                        break;

                    case 4:
                        text = "Speaker Allocation"; key = "ROOT_BLOCK";
                        break;

                    case 5:
                        text = "VESA Display Transfer Characteristic"; key = "ROOT_BLOCK";
                        break;

                    case 7:
                        int ext_tag = edid[offset + 1];
                        switch (ext_tag)
                        {
                            case 0:
                                text = "Video Capability"; key = "ROOT_BLOCK";
                                break;

                            case 1:
                                int vendor = edid[offset + 2] | (edid[offset + 3] << 8) | (edid[offset + 4] << 16);
                                switch (vendor)
                                {
                                    case 0x00D046:
                                        text = "Dolby Vendor Specific Video"; key = "ROOT_BLOCK";
                                        break;

                                    case 0x90848B:
                                        text = "HDR10+ Video"; key = "ROOT_BLOCK";
                                        break;

                                    default:
                                        text = "Vendor Specific Video"; key = "ROOT_BLOCK";
                                        break;
                                }
                                break;

                            case 2:
                                text = "VESA Display Device"; key = "ROOT_BLOCK";
                                break;

                            case 3:
                                text = "Reserved for VESA Video"; key = "ROOT_BLOCK";
                                break;

                            case 4:
                                text = "Reserved for HDMI Video"; key = "ROOT_BLOCK";
                                break;

                            case 5:
                                text = "Colorimetry"; key = "ROOT_BLOCK";
                                break;

                            case 6:
                                text = "HDR Static Metadata"; key = "ROOT_BLOCK";
                                break;

                            case 7:
                                text = "HDR Dynamic Metadata"; key = "ROOT_BLOCK";
                                break;

                            case 8:
                            case 9:
                            case 10:
                            case 11:
                            case 12:
                                text = "Reserved for video-related blocks"; key = "ROOT_BLOCK";
                                break;

                            case 13:
                                text = "Video Format Preference"; key = "ROOT_BLOCK";
                                break;

                            case 14:
                                text = "YCbCr4:2:0 Video"; key = "ROOT_BLOCK";
                                break;

                            case 15:
                                text = "YCbCr4:2:0 Capability Map"; key = "ROOT_BLOCK";
                                break;

                            case 16:
                                text = "Reserved for CTA Miscellaneous Audio Field"; key = "ROOT_BLOCK";
                                break;

                            case 17:
                                text = "Vendor-Specific Audio"; key = "ROOT_BLOCK";
                                break;

                            case 18:
                                text = "HDMI Audio"; key = "ROOT_BLOCK";
                                break;

                            case 19:
                                text = "Room Configuration"; key = "ROOT_BLOCK";
                                break;

                            case 20:
                                text = "Speaker Location"; key = "ROOT_BLOCK";
                                break;

                            case 21:
                            case 22:
                            case 23:
                            case 24:
                            case 25:
                            case 26:
                            case 27:
                            case 28:
                            case 29:
                            case 30:
                            case 31:
                                text = "Reserved for audio-related blocks"; key = "ROOT_BLOCK";
                                break;

                            case 32:
                                text = "InfoFrame"; key = "ROOT_BLOCK";
                                break;

                            case 34:
                                text = "DisplayID Type VII Video Timing"; key = "ROOT_BLOCK";
                                break;

                            case 35:
                                text = "DisplayID Type VIII Video Timing"; key = "ROOT_BLOCK";
                                break;

                            case 42:
                                text = "DisplayID Type X Video Timing"; key = "ROOT_BLOCK";
                                break;

                            case 120:
                                text = "HDMI Forum EDID Extension Override"; key = "ROOT_BLOCK";
                                break;

                            case 121:
                                text = "HDMI Forum Sink Capability"; key = "ROOT_BLOCK";
                                break;

                            case 122:
                            case 123:
                            case 124:
                            case 125:
                            case 126:
                            case 127:
                                text = "Reserved for HDMI"; key = "ROOT_BLOCK";
                                break;

                            default:
                                text = "Reserved"; key = "ROOT_BLOCK";
                                break;
                        }
                        break;
                }

                Tree_Add(ext_tree, text, key, block, extension_tag, offset, block_length + 1, 2, false, "Data Blocks");
                offset += block_length + 1;
            } while (offset < dtd_pos);

            //DTD block
            int count = DTD_Count(block, edid);

            if (count > 0)
            {
                Tree_Add(ext_tree, "Detailed Timing Descriptors", "NODE_BLOCK", block, extension_tag, offset, 0, 1, true, "CTA Block");
                for (int i = 0; i < count; i++)
                {
                    text = "DTD " + (i + 1); key = "ROOT_BLOCK";
                    Tree_Add(ext_tree, text, key, block, extension_tag, offset, 18, 2, false, "Detailed Timing Descriptors");
                    offset += 18;
                }
            }

            offset = (block + 1) * 128 - 1;
            Tree_Add(ext_tree, "Checksum", "ROOT_BLOCK", block, extension_tag, offset, 1, 1, false, "CTA Block");
            Tree_Build(ext_tree, treeView);
        }

        private void OtherBlock_Tree(int block, byte[] edid, TreeView treeView)
        {
            int offset = block * 128;
            byte extension_tag = edid[offset];
            var ext_tree = new List<TreeNode>();

            Tree_Add(ext_tree, "Unknown Block", "NODE_BLOCK", block, extension_tag, offset, 128, 0, true, null);
            Tree_Add(ext_tree, "Data", "ROOT_BLOCK", block, extension_tag, offset, 128, 1, false, "Unknown Block");
            Tree_Build(ext_tree, treeView);
        }

        private void ExtBlock_Tree(int block, byte[] edid, TreeView treeView)
        {
            //refer: VESA E-EDID Release A Rev2 table2.7
            byte extension_tag = edid[block * 128];

            switch (extension_tag)
            {
                case 0x02:
                    CTABlock_Tree(block, edid, treeView);
                    break;

                default:
                    OtherBlock_Tree(block, edid, treeView);
                    break;
            }
        }

        public void ExtBlock(byte[] edid, TreeView treeView)
        {
            int extblock_cnt = edid[126], i = 1;
            if (extblock_cnt == 0) return;

            do
            {
                ExtBlock_Tree(i, edid, treeView);
            }while(i++ < extblock_cnt);
        }

        public void BaseBlock(TreeView treeView)
        {
            var base_tree = new List<TreeNode>();

            Tree_Add(base_tree, "Base Block", "NODE_BLOCK", 0, 0, 0, 128, 0, true, null);
            Tree_Add(base_tree, "Header", "ROOT_BLOCK", 0, 0, 0, 8, 1, false, "Base Block");
            Tree_Add(base_tree, "Vendor/Product Information", "ROOT_BLOCK", 0, 0, 8, 10, 1, false, "Base Block");
            Tree_Add(base_tree, "EDID Version/Revision", "ROOT_BLOCK", 0, 0, 18, 2, 1, false, "Base Block");
            Tree_Add(base_tree, "Basic Display/Features", "NODE_BLOCK", 0, 0, 20, 5, 1, true, "Base Block");
            Tree_Add(base_tree, "Video Input Definition", "ROOT_BLOCK", 0, 0, 20, 1, 2, false, "Basic Display/Features");
            Tree_Add(base_tree, "Hors/Vert Screen Size", "ROOT_BLOCK", 0, 0, 21, 2, 2, false, "Basic Display/Features");
            Tree_Add(base_tree, "Display Transfer Characteristic", "ROOT_BLOCK", 0, 0, 23, 1, 2, false, "Basic Display/Features");
            Tree_Add(base_tree, "Feature Support", "ROOT_BLOCK", 0, 0, 24, 1, 2, false, "Basic Display/Features");
            Tree_Add(base_tree, "Color Characteristics", "ROOT_BLOCK", 0, 0, 25, 10, 1, false, "Base Block");
            Tree_Add(base_tree, "Established Timings", "ROOT_BLOCK", 0, 0, 35, 3, 1, false, "Base Block");
            Tree_Add(base_tree, "Standard Timings", "NODE_BLOCK", 0, 0, 38, 16, 1, true, "Base Block");
            Tree_Add(base_tree, "Standard Timing 1", "ROOT_BLOCK", 0, 0, 38, 2, 2, false, "Standard Timings");
            Tree_Add(base_tree, "Standard Timing 2", "ROOT_BLOCK", 0, 0, 40, 2, 2, false, "Standard Timings");
            Tree_Add(base_tree, "Standard Timing 3", "ROOT_BLOCK", 0, 0, 42, 2, 2, false, "Standard Timings");
            Tree_Add(base_tree, "Standard Timing 4", "ROOT_BLOCK", 0, 0, 44, 2, 2, false, "Standard Timings");
            Tree_Add(base_tree, "Standard Timing 5", "ROOT_BLOCK", 0, 0, 46, 2, 2, false, "Standard Timings");
            Tree_Add(base_tree, "Standard Timing 6", "ROOT_BLOCK", 0, 0, 48, 2, 2, false, "Standard Timings");
            Tree_Add(base_tree, "Standard Timing 7", "ROOT_BLOCK", 0, 0, 50, 2, 2, false, "Standard Timings");
            Tree_Add(base_tree, "Standard Timing 8", "ROOT_BLOCK", 0, 0, 52, 2, 2, false, "Standard Timings");
            Tree_Add(base_tree, "Detailed Timing/Display Descriptors", "NODE_BLOCK", 0, 0, 54, 72, 1, true, "Base Block");
            Tree_Add(base_tree, "Detailed Timing/Descriptor 1", "ROOT_BLOCK", 0, 0, 54, 18, 2, false, "Detailed Timing/Display Descriptors");
            Tree_Add(base_tree, "Detailed Timing/Descriptor 2", "ROOT_BLOCK", 0, 0, 72, 18, 2, false, "Detailed Timing/Display Descriptors");
            Tree_Add(base_tree, "Detailed Timing/Descriptor 3", "ROOT_BLOCK", 0, 0, 90, 18, 2, false, "Detailed Timing/Display Descriptors");
            Tree_Add(base_tree, "Detailed Timing/Descriptor 4", "ROOT_BLOCK", 0, 0, 108, 18, 2, false, "Detailed Timing/Display Descriptors");
            Tree_Add(base_tree, "Extension Flag", "ROOT_BLOCK", 0, 0, 126, 1, 1, false, "Base Block");
            Tree_Add(base_tree, "Checksum", "ROOT_BLOCK", 0, 0, 127, 1, 1, false, "Base Block");
            Tree_Build(base_tree, treeView);
        }
    }
}
