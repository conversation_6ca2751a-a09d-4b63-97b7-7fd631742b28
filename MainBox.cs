﻿using System;
using Common;
using System.IO;
using TreeBuild;
using DataParser;
using System.Text;
using System.Linq;
using Microsoft.Win32;
using System.Management;
using System.Collections;
using System.Windows.Forms;
using EDID_Manager.Properties;
using System.Collections.Generic;
using System.Runtime.InteropServices;

namespace EDID_Manager
{
    public partial class MainBox : Form
    {
        private byte[] edid_data;
        private bool edid_found = false;
        private readonly ToolTip toolTip1 = new ToolTip
        {
            AutoPopDelay = 3000,
            InitialDelay = 200,
            ReshowDelay = 200,
            ShowAlways = true
        };

        public MainBox()
        {
            InitializeComponent();
        }

        [DllImport("user32", EntryPoint = "HideCaret")]
        private static extern bool HideCaret(IntPtr hWnd);

        void TextBox1_GotFocus(object sender, EventArgs e)
        {
            HideCaret((sender as TextBox).Handle);
        }

        void TextBox1_MouseDown(object sender, MouseEventArgs e)
        {
            HideCaret((sender as TextBox).Handle);
        }

        void TextBox2_GotFocus(object sender, EventArgs e)
        {
            HideCaret((sender as TextBox).Handle);
        }

        void TextBox2_MouseDown(object sender, MouseEventArgs e)
        {
            HideCaret((sender as TextBox).Handle);
        }

        private void Tooltips_initial()
        {
            toolTip1.SetToolTip(this.pictureBox1, "read edid");
            toolTip1.SetToolTip(this.pictureBox2, "save edid");
            toolTip1.SetToolTip(this.pictureBox3, "about");
            toolTip1.SetToolTip(this.pictureBox4, "load edid");
        }

        private void InitImageList()
        {
            var imagelist = new List<Images>()
            {
                new Images() { Key = "ROOT_BLOCK", Image = Resources.root },
                new Images() { Key = "NODE_BLOCK", Image = Resources.node },
            };

            treeView1.ImageList = new ImageList();
            for (int i = 0; i < imagelist.Count; i++)
            {
                treeView1.ImageList.Images.Add(imagelist[i].Key, imagelist[i].Image);
            }
        }

        internal static class NativeWinAPI
        {
            internal static readonly int gwl_style = -20;
            internal static readonly int ws_composite = 0x02000000;

            [DllImport("user32")]
            internal static extern int GetWindowLong(IntPtr hWnd, int nIndex);

            [DllImport("user32")]
            internal static extern int SetWindowLong(IntPtr hWnd, int nIndex, int dwNewLong);
        }

        private void NodePicBlink()
        {
            SetStyle(ControlStyles.DoubleBuffer, true);

            int style = NativeWinAPI.GetWindowLong(Handle, NativeWinAPI.gwl_style);
            style |= NativeWinAPI.ws_composite;

            NativeWinAPI.SetWindowLong(Handle, NativeWinAPI.gwl_style, style);
        }

        private void MainBox_Load(object sender, EventArgs e)
        {
            NodePicBlink();
            InitImageList();
            Tooltips_initial();

            textBox1.GotFocus += TextBox1_GotFocus;
            textBox1.MouseDown += TextBox1_MouseDown;

            textBox2.GotFocus += TextBox2_GotFocus;
            textBox2.MouseDown += TextBox2_MouseDown;

            textBox3.GotFocus += TextBox3_GotFocus;
            textBox3.MouseDown += TextBox3_MouseDown;

            // 初始化日志系统
            LogHelper.Initialize(textBox3);
            LogHelper.LogInfo("EDID Manager 已启动");
        }

        void TextBox3_GotFocus(object sender, EventArgs e)
        {
            HideCaret((sender as TextBox).Handle);
        }

        void TextBox3_MouseDown(object sender, MouseEventArgs e)
        {
            HideCaret((sender as TextBox).Handle);
        }

        private void EDID_Tree(byte[] edid)
        {
            Trees tree = new Trees();
            try
            {
                treeView1.BeginUpdate();

                textBox1.Clear();
                textBox2.Clear();
                treeView1.Nodes.Clear();
                dataGridView1.Rows.Clear();

                tree.BaseBlock(treeView1);
                tree.ExtBlock(edid, treeView1);

                LogHelper.LogInfo("EDID树结构已生成");
            }
            catch (Exception ex)
            {
                LogHelper.LogError("生成EDID树结构时出错", ex);
            }
            finally
            {
                treeView1.EndUpdate();
            }
        }

        private void EDID_Proc(string file)
        {
            try
            {
                LogHelper.LogInfo($"正在处理EDID文件: {file}");
                FileStream fileStream = new FileStream(file, FileMode.Open);
                BinaryReader br = new BinaryReader(fileStream, Encoding.UTF8);

                int file_len = (int)fileStream.Length;
                if ((file_len % 128) != 0)
                {
                    string errorMsg = "文件长度不是128的倍数";
                    LogHelper.LogError(errorMsg);
                }
                else
                {
                    edid_data = br.ReadBytes(file_len);

                    if (edid_data[0] == 0x00 && edid_data[1] == 0xFF && edid_data[2] == 0xFF && edid_data[3] == 0xFF &&
                        edid_data[4] == 0xFF && edid_data[5] == 0xFF && edid_data[6] == 0xFF && edid_data[7] == 0x00)
                    {
                        edid_found = true;
                        EDID_Tree(edid_data);
                        LogHelper.LogSuccess($"成功加载EDID文件，大小: {file_len} 字节");
                    }
                    else
                    {
                        string errorMsg = "非EDID文件格式";
                        LogHelper.LogError(errorMsg);
                    }
                }
                br.Close();
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"处理EDID文件时出错: {file}", ex);
            }
        }

        [DllImport("kernel32.dll", EntryPoint = "CopyMemory", SetLastError = false)]
        public static extern void CopyMemory(IntPtr dest, IntPtr src, uint count);

        // 用于EnumDisplayDevices API的结构体和导入
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Auto)]
        public struct DISPLAY_DEVICE
        {
            public int cb;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)]
            public string DeviceName;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 128)]
            public string DeviceString;
            public int StateFlags;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 128)]
            public string DeviceID;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 128)]
            public string DeviceKey;
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        public static extern bool EnumDisplayDevices(string lpDevice, uint iDevNum, ref DISPLAY_DEVICE lpDisplayDevice, uint dwFlags);

        // 定义一个结构体来存储显示器信息
        private class DisplayInfo
        {
            public string DeviceID { get; set; }
            public string DisplayName { get; set; }
            public byte[] EdidData { get; set; }
            public bool IsActive { get; set; }
        }

        // 检查EDID数据是否有效
        private bool IsValidEdid(byte[] edid)
        {
            if (edid == null || edid.Length < 128)
                return false;

            // 检查EDID头部标识
            return edid[0] == 0x00 && edid[1] == 0xFF && edid[2] == 0xFF && edid[3] == 0xFF &&
                   edid[4] == 0xFF && edid[5] == 0xFF && edid[6] == 0xFF && edid[7] == 0x00;
        }

        // 检查是否在VMware虚拟环境中
        private bool IsRunningInVMware()
        {
            try
            {
                LogHelper.LogInfo("检查是否在VMware虚拟环境中...");

                // 检查显卡驱动是否为VMware
                try
                {
                    using (var searcher = new ManagementObjectSearcher("SELECT Name FROM Win32_VideoController"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            string name = obj["Name"]?.ToString() ?? "";
                            LogHelper.LogInfo($"检测到显卡: {name}");
                            if (name.Contains("VMware") || name.Contains("VM SVGA"))
                            {
                                LogHelper.LogInfo("检测到VMware显卡");
                                return true;
                            }
                        }
                    }
                }
                catch (ManagementException ex)
                {
                    LogHelper.LogError("检查VMware显卡时出错", ex);
                }

                // 检查系统制造商
                try
                {
                    using (var searcher = new ManagementObjectSearcher("SELECT Manufacturer FROM Win32_ComputerSystem"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            string manufacturer = obj["Manufacturer"]?.ToString() ?? "";
                            LogHelper.LogInfo($"检测到系统制造商: {manufacturer}");
                            if (manufacturer.Contains("VMware"))
                            {
                                LogHelper.LogInfo("检测到VMware系统制造商");
                                return true;
                            }
                        }
                    }
                }
                catch (ManagementException ex)
                {
                    LogHelper.LogError("检查VMware系统制造商时出错", ex);
                }
            }
            catch (Exception ex)
            {
                LogHelper.LogError("检查VMware环境时出错", ex);
            }

            LogHelper.LogInfo("未检测到VMware环境");
            return false;
        }

        // 为VMware环境创建一个基本的EDID数据
        private byte[] CreateDefaultEdidForVMware()
        {
            // 创建一个基本的EDID 1.3数据，适用于1920x1080@60Hz显示器
            byte[] edid = new byte[128];

            // EDID头部
            edid[0] = 0x00; edid[1] = 0xFF; edid[2] = 0xFF; edid[3] = 0xFF;
            edid[4] = 0xFF; edid[5] = 0xFF; edid[6] = 0xFF; edid[7] = 0x00;

            // 制造商ID "VMW"
            edid[8] = 0x06; edid[9] = 0x10;

            // 产品ID
            edid[10] = 0x01; edid[11] = 0x00;

            // 序列号
            edid[12] = 0x01; edid[13] = 0x00; edid[14] = 0x00; edid[15] = 0x00;

            // 制造周和年
            edid[16] = 0x01; edid[17] = 0x1E; // 2020年第1周

            // EDID版本和修订版本
            edid[18] = 0x01; edid[19] = 0x03; // 版本1.3

            // 基本显示参数
            edid[20] = 0x80; // 数字输入，8位每颜色
            edid[21] = 0x50; // 水平尺寸: 80cm
            edid[22] = 0x2D; // 垂直尺寸: 45cm
            edid[23] = 0x78; // 伽马: 2.2
            edid[24] = 0x00; // 特性

            // 色度坐标
            edid[25] = 0xEE; edid[26] = 0x91; edid[27] = 0xA3;
            edid[28] = 0x54; edid[29] = 0x4C; edid[30] = 0x99;
            edid[31] = 0x26; edid[32] = 0x0F; edid[33] = 0x50; edid[34] = 0x54;

            // 建立的时序
            edid[35] = 0x00; edid[36] = 0x00; edid[37] = 0x00;

            // 标准时序
            for (int i = 38; i < 54; i++) edid[i] = 0x01;

            // 详细时序描述 - 1920x1080@60Hz
            // 详细时序1
            edid[54] = 0x00; edid[55] = 0x00; edid[56] = 0x00; edid[57] = 0x00;
            edid[58] = 0x00; edid[59] = 0x00; edid[60] = 0x00; edid[61] = 0x00;
            edid[62] = 0x00; edid[63] = 0x00; edid[64] = 0x00; edid[65] = 0x00;
            edid[66] = 0x00; edid[67] = 0x00; edid[68] = 0x00; edid[69] = 0x00;
            edid[70] = 0x00; edid[71] = 0x00;

            // 详细时序2 - 显示器名称
            edid[72] = 0x00; edid[73] = 0x00; edid[74] = 0x00; edid[75] = 0xFC;
            edid[76] = 0x00; // "VMware Virtual Display"
            edid[77] = 0x56; edid[78] = 0x4D; edid[79] = 0x77; edid[80] = 0x61;
            edid[81] = 0x72; edid[82] = 0x65; edid[83] = 0x20; edid[84] = 0x44;
            edid[85] = 0x69; edid[86] = 0x73; edid[87] = 0x70; edid[88] = 0x6C;
            edid[89] = 0x00;

            // 详细时序3
            for (int i = 90; i < 108; i++) edid[i] = 0x00;

            // 详细时序4
            for (int i = 108; i < 126; i++) edid[i] = 0x00;

            // 扩展块数量
            edid[126] = 0x00;

            // 计算校验和
            byte checksum = 0;
            for (int i = 0; i < 127; i++) checksum += edid[i];
            edid[127] = (byte)(256 - checksum);

            return edid;
        }

        // 从WMI获取EDID数据
        private List<DisplayInfo> GetDisplayInfoFromWMI()
        {
            var displays = new List<DisplayInfo>();

            try
            {
                LogHelper.LogInfo("尝试从WMI获取EDID数据...");

                // 获取活动显示器的实例名称
                var activeDisplays = new Dictionary<string, string>();
                try
                {
                    using (var searcher = new ManagementObjectSearcher("root\\WMI", "SELECT * FROM WmiMonitorBasicDisplayParams"))
                    {
                        ManagementObjectCollection collection = searcher.Get();
                        LogHelper.LogInfo($"WmiMonitorBasicDisplayParams查询返回 {collection.Count} 个结果");

                        foreach (ManagementObject obj in collection)
                        {
                            try
                            {
                                string instanceName = obj["InstanceName"]?.ToString() ?? "";
                                if (!string.IsNullOrEmpty(instanceName))
                                {
                                    activeDisplays[instanceName] = instanceName;
                                    LogHelper.LogInfo($"找到活动显示器: {instanceName}");
                                }
                            }
                            catch (Exception ex)
                            {
                                LogHelper.LogError("处理WmiMonitorBasicDisplayParams对象时出错", ex);
                            }
                        }
                    }
                }
                catch (ManagementException ex)
                {
                    LogHelper.LogError("查询WmiMonitorBasicDisplayParams时出错", ex);
                    // 尝试使用备用WMI命名空间
                    try
                    {
                        LogHelper.LogInfo("尝试使用备用WMI命名空间...");
                        using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_DesktopMonitor"))
                        {
                            foreach (ManagementObject obj in searcher.Get())
                            {
                                try
                                {
                                    string deviceId = obj["DeviceID"]?.ToString() ?? "";
                                    if (!string.IsNullOrEmpty(deviceId))
                                    {
                                        activeDisplays[deviceId] = deviceId;
                                        LogHelper.LogInfo($"找到备用显示器: {deviceId}");
                                    }
                                }
                                catch (Exception innerEx)
                                {
                                    LogHelper.LogError("处理Win32_DesktopMonitor对象时出错", innerEx);
                                }
                            }
                        }
                    }
                    catch (Exception backupEx)
                    {
                        LogHelper.LogError("使用备用WMI命名空间时出错", backupEx);
                    }
                }

                // 获取显示器的EDID数据
                if (activeDisplays.Count > 0)
                {
                    try
                    {
                        using (var searcher = new ManagementObjectSearcher("root\\WMI", "SELECT * FROM WmiMonitorDescriptorMethods"))
                        {
                            ManagementObjectCollection collection = searcher.Get();
                            LogHelper.LogInfo($"WmiMonitorDescriptorMethods查询返回 {collection.Count} 个结果");

                            foreach (ManagementObject obj in collection)
                            {
                                try
                                {
                                    string instanceName = obj["InstanceName"]?.ToString() ?? "";
                                    if (string.IsNullOrEmpty(instanceName))
                                    {
                                        LogHelper.LogWarning("WMI对象的InstanceName为空");
                                        continue;
                                    }

                                    bool isActive = activeDisplays.ContainsKey(instanceName);
                                    LogHelper.LogInfo($"处理显示器: {instanceName}, 活动状态: {isActive}");

                                    // 调用GetEdidData方法获取EDID数据
                                    byte[] edidData = null;
                                    try
                                    {
                                        ManagementBaseObject outParams = obj.InvokeMethod("GetEdidData", null, null);
                                        if (outParams != null)
                                        {
                                            byte[] rawEdid = outParams["EdidData"] as byte[];
                                            if (IsValidEdid(rawEdid))
                                            {
                                                edidData = rawEdid;
                                                LogHelper.LogInfo($"成功获取显示器 {instanceName} 的EDID数据");
                                            }
                                            else
                                            {
                                                LogHelper.LogWarning($"显示器 {instanceName} 的EDID数据无效");
                                            }
                                        }
                                        else
                                        {
                                            LogHelper.LogWarning($"显示器 {instanceName} 的GetEdidData方法返回空");
                                        }
                                    }
                                    catch (Exception methodEx)
                                    {
                                        LogHelper.LogError($"调用显示器 {instanceName} 的GetEdidData方法时出错", methodEx);
                                    }

                                    if (edidData != null)
                                    {
                                        string[] nameParts = instanceName.Split(new char[] { '\\' }, StringSplitOptions.RemoveEmptyEntries);
                                        string deviceId = nameParts.Length > 1 ? nameParts[1] : instanceName;

                                        displays.Add(new DisplayInfo
                                        {
                                            DeviceID = deviceId,
                                            DisplayName = instanceName,
                                            EdidData = edidData,
                                            IsActive = isActive
                                        });
                                        LogHelper.LogSuccess($"添加显示器: {deviceId}, EDID数据大小: {edidData.Length} 字节");
                                    }
                                }
                                catch (Exception objEx)
                                {
                                    LogHelper.LogError("处理WmiMonitorDescriptorMethods对象时出错", objEx);
                                    // 忽略单个显示器的错误，继续处理其他显示器
                                }
                            }
                        }
                    }
                    catch (ManagementException ex)
                    {
                        LogHelper.LogError("查询WmiMonitorDescriptorMethods时出错", ex);
                    }
                }
                else
                {
                    LogHelper.LogWarning("未找到活动显示器");
                }
            }
            catch (Exception ex)
            {
                LogHelper.LogError("从WMI获取EDID数据时出错", ex);
                // 如果WMI方法失败，返回空列表
            }

            LogHelper.LogInfo($"WMI方法找到 {displays.Count} 个显示器");
            return displays;
        }

        // 从注册表获取EDID数据
        private List<DisplayInfo> GetDisplayInfoFromRegistry()
        {
            var displays = new List<DisplayInfo>();

            try
            {
                // 尝试从多个可能的注册表路径获取EDID数据
                string[] registryPaths = new string[]
                {
                    @"SYSTEM\CurrentControlSet\Enum\DISPLAY",
                    @"SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}",
                    @"SYSTEM\CurrentControlSet\Control\GraphicsDrivers\Configuration"
                };

                foreach (string path in registryPaths)
                {
                    try
                    {
                        using (RegistryKey baseKey = Registry.LocalMachine.OpenSubKey(path))
                        {
                            if (baseKey != null)
                            {
                                // 第一种路径：SYSTEM\CurrentControlSet\Enum\DISPLAY
                                if (path.EndsWith("DISPLAY"))
                                {
                                    foreach (string displayName in baseKey.GetSubKeyNames())
                                    {
                                        using (RegistryKey displayKey = baseKey.OpenSubKey(displayName))
                                        {
                                            if (displayKey != null)
                                            {
                                                foreach (string subKeyName in displayKey.GetSubKeyNames())
                                                {
                                                    using (RegistryKey subKey = displayKey.OpenSubKey(subKeyName))
                                                    {
                                                        if (subKey != null)
                                                        {
                                                            using (RegistryKey deviceParams = subKey.OpenSubKey("Device Parameters"))
                                                            {
                                                                if (deviceParams != null)
                                                                {
                                                                    byte[] edidData = deviceParams.GetValue("EDID") as byte[];
                                                                    if (IsValidEdid(edidData))
                                                                    {
                                                                        displays.Add(new DisplayInfo
                                                                        {
                                                                            DeviceID = displayName,
                                                                            DisplayName = $"{displayName}\\{subKeyName}",
                                                                            EdidData = edidData,
                                                                            IsActive = true // 假设从注册表获取的都是活动显示器
                                                                        });
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                // 第二种路径：显卡驱动类路径
                                else if (path.Contains("Class"))
                                {
                                    foreach (string subKeyName in baseKey.GetSubKeyNames())
                                    {
                                        if (subKeyName != "Properties")
                                        {
                                            using (RegistryKey subKey = baseKey.OpenSubKey(subKeyName))
                                            {
                                                if (subKey != null)
                                                {
                                                    byte[] edidData = subKey.GetValue("EDID") as byte[];
                                                    if (IsValidEdid(edidData))
                                                    {
                                                        string deviceDesc = subKey.GetValue("DeviceDesc") as string ?? subKeyName;
                                                        displays.Add(new DisplayInfo
                                                        {
                                                            DeviceID = subKeyName,
                                                            DisplayName = deviceDesc,
                                                            EdidData = edidData,
                                                            IsActive = true
                                                        });
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                // 第三种路径：GraphicsDrivers配置路径
                                else if (path.Contains("GraphicsDrivers"))
                                {
                                    foreach (string configId in baseKey.GetSubKeyNames())
                                    {
                                        using (RegistryKey configKey = baseKey.OpenSubKey(configId))
                                        {
                                            if (configKey != null)
                                            {
                                                foreach (string sourceId in configKey.GetSubKeyNames())
                                                {
                                                    using (RegistryKey sourceKey = configKey.OpenSubKey(sourceId))
                                                    {
                                                        if (sourceKey != null)
                                                        {
                                                            byte[] edidData = sourceKey.GetValue("EDID") as byte[];
                                                            if (IsValidEdid(edidData))
                                                            {
                                                                displays.Add(new DisplayInfo
                                                                {
                                                                    DeviceID = configId,
                                                                    DisplayName = sourceId,
                                                                    EdidData = edidData,
                                                                    IsActive = true
                                                                });
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    catch
                    {
                        // 忽略单个路径的错误，继续尝试其他路径
                    }
                }
            }
            catch (Exception)
            {
                // 如果注册表方法失败，返回空列表
            }

            return displays;
        }

        // 使用EnumDisplayDevices API获取显示器信息
        private List<DisplayInfo> GetDisplayInfoFromEnumDisplayDevices()
        {
            var displays = new List<DisplayInfo>();

            try
            {
                uint deviceIndex = 0;
                var device = new DISPLAY_DEVICE();
                device.cb = Marshal.SizeOf(device);

                // 枚举所有显示设备
                while (EnumDisplayDevices(null, deviceIndex, ref device, 0))
                {
                    // 检查是否是活动显示器
                    bool isActive = (device.StateFlags & 0x1) != 0; // DISPLAY_DEVICE_ATTACHED_TO_DESKTOP

                    // 从设备键中提取注册表路径
                    if (!string.IsNullOrEmpty(device.DeviceKey))
                    {
                        string regPath = device.DeviceKey.Replace("\\Registry\\Machine\\", "");

                        try
                        {
                            using (RegistryKey deviceKey = Registry.LocalMachine.OpenSubKey(regPath))
                            {
                                if (deviceKey != null)
                                {
                                    // 尝试在设备参数中查找EDID
                                    using (RegistryKey deviceParams = deviceKey.OpenSubKey("Device Parameters"))
                                    {
                                        if (deviceParams != null)
                                        {
                                            byte[] edidData = deviceParams.GetValue("EDID") as byte[];
                                            if (IsValidEdid(edidData))
                                            {
                                                displays.Add(new DisplayInfo
                                                {
                                                    DeviceID = device.DeviceID,
                                                    DisplayName = device.DeviceString,
                                                    EdidData = edidData,
                                                    IsActive = isActive
                                                });
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        catch
                        {
                            // 忽略单个设备的错误
                        }
                    }

                    // 重置设备结构体，准备获取下一个设备
                    device = new DISPLAY_DEVICE();
                    device.cb = Marshal.SizeOf(device);
                    deviceIndex++;
                }
            }
            catch (Exception)
            {
                // 如果API调用失败，返回空列表
            }

            return displays;
        }

        // 主要的EDID获取方法
        private string[] DisplayList()
        {
            var displayList = new ArrayList();
            bool edidFound = false;

            try
            {
                LogHelper.LogInfo("开始获取显示器EDID数据...");

                // 检查是否在VMware虚拟环境中
                bool isVMware = IsRunningInVMware();
                if (isVMware)
                {
                    LogHelper.LogInfo("检测到VMware虚拟环境");
                }

                // 尝试多种方法获取EDID数据
                List<DisplayInfo> allDisplays = new List<DisplayInfo>();

                // 方法1: 使用WMI
                LogHelper.LogInfo("尝试使用WMI方法获取EDID数据...");
                var wmiDisplays = GetDisplayInfoFromWMI();
                if (wmiDisplays.Count > 0)
                {
                    LogHelper.LogSuccess($"通过WMI找到 {wmiDisplays.Count} 个显示设备");
                    allDisplays.AddRange(wmiDisplays);
                }
                else
                {
                    LogHelper.LogInfo("WMI方法未找到显示设备");
                }

                // 方法2: 使用EnumDisplayDevices API
                if (allDisplays.Count == 0)
                {
                    LogHelper.LogInfo("尝试使用EnumDisplayDevices API获取EDID数据...");
                    var enumDisplays = GetDisplayInfoFromEnumDisplayDevices();
                    if (enumDisplays.Count > 0)
                    {
                        LogHelper.LogSuccess($"通过EnumDisplayDevices找到 {enumDisplays.Count} 个显示设备");
                        allDisplays.AddRange(enumDisplays);
                    }
                    else
                    {
                        LogHelper.LogInfo("EnumDisplayDevices方法未找到显示设备");
                    }
                }

                // 方法3: 使用注册表
                if (allDisplays.Count == 0)
                {
                    LogHelper.LogInfo("尝试从注册表获取EDID数据...");
                    var registryDisplays = GetDisplayInfoFromRegistry();
                    if (registryDisplays.Count > 0)
                    {
                        LogHelper.LogSuccess($"通过注册表找到 {registryDisplays.Count} 个显示设备");
                        allDisplays.AddRange(registryDisplays);
                    }
                    else
                    {
                        LogHelper.LogInfo("注册表方法未找到显示设备");
                    }
                }

                // 处理找到的显示器
                if (allDisplays.Count > 0)
                {
                    LogHelper.LogInfo($"总共找到 {allDisplays.Count} 个显示设备");

                    // 优先选择活动显示器
                    var activeDisplays = allDisplays.Where(d => d.IsActive).ToList();
                    var displaysToUse = activeDisplays.Count > 0 ? activeDisplays : allDisplays;

                    if (activeDisplays.Count > 0)
                    {
                        LogHelper.LogInfo($"其中 {activeDisplays.Count} 个为活动显示设备");
                    }

                    // 使用第一个有效的显示器的EDID数据
                    var firstDisplay = displaysToUse.FirstOrDefault();
                    if (firstDisplay != null && IsValidEdid(firstDisplay.EdidData))
                    {
                        displayList.Add(firstDisplay.DeviceID);
                        LogHelper.LogInfo($"使用显示设备: {firstDisplay.DisplayName}");

                        if (!edid_found)
                        {
                            edid_found = true;
                            edid_data = new byte[firstDisplay.EdidData.Length];
                        }

                        Buffer.BlockCopy(firstDisplay.EdidData, 0, edid_data, 0, firstDisplay.EdidData.Length);
                        edidFound = true;
                    }

                    // 添加所有找到的显示器到列表中
                    foreach (var display in displaysToUse)
                    {
                        if (!displayList.Contains(display.DeviceID))
                        {
                            displayList.Add(display.DeviceID);
                        }
                    }
                }
                else
                {
                    LogHelper.LogWarning("未找到任何显示设备");
                }

                // 如果在VMware环境中且没有找到EDID数据，使用默认的VMware EDID
                if (isVMware && !edidFound)
                {
                    LogHelper.LogInfo("在VMware环境中使用默认EDID数据");
                    byte[] vmwareEdid = CreateDefaultEdidForVMware();

                    if (IsValidEdid(vmwareEdid))
                    {
                        displayList.Add("VMware Virtual Display");

                        if (!edid_found)
                        {
                            edid_found = true;
                            edid_data = new byte[vmwareEdid.Length];
                        }

                        Buffer.BlockCopy(vmwareEdid, 0, edid_data, 0, vmwareEdid.Length);
                        edidFound = true;
                        LogHelper.LogSuccess("成功创建VMware虚拟显示器的EDID数据");
                    }
                }

                // 如果在VMware环境中，尝试从VMware显示适配器获取分辨率信息
                if (isVMware)
                {
                    try
                    {
                        LogHelper.LogInfo("尝试获取VMware显示适配器信息...");

                        // 使用更安全的WMI查询方式
                        using (var searcher = new ManagementObjectSearcher("SELECT Name FROM Win32_VideoController"))
                        {
                            ManagementObjectCollection collection = searcher.Get();
                            LogHelper.LogInfo($"Win32_VideoController查询返回 {collection.Count} 个结果");

                            foreach (ManagementObject obj in collection)
                            {
                                try
                                {
                                    string name = obj["Name"]?.ToString() ?? "";
                                    if (!string.IsNullOrEmpty(name))
                                    {
                                        LogHelper.LogInfo($"找到显示适配器: {name}");
                                        if (name.Contains("VMware") || name.Contains("VM SVGA"))
                                        {
                                            if (!displayList.Contains(name))
                                            {
                                                displayList.Add(name);
                                                LogHelper.LogSuccess($"添加VMware显示适配器: {name}");
                                            }
                                        }
                                    }
                                }
                                catch (Exception itemEx)
                                {
                                    LogHelper.LogError("处理显示适配器信息时出错", itemEx);
                                }
                            }
                        }
                    }
                    catch (ManagementException ex)
                    {
                        LogHelper.LogError("查询VMware显示适配器信息时出错", ex);

                        // 如果WMI查询失败，添加一个默认的VMware显示适配器名称
                        string defaultName = "VMware Virtual Display";
                        if (!displayList.Contains(defaultName))
                        {
                            displayList.Add(defaultName);
                            LogHelper.LogInfo($"添加默认VMware显示适配器: {defaultName}");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.LogError("获取VMware显示适配器信息时出错", ex);
                    }
                }

                if (edidFound)
                {
                    LogHelper.LogSuccess($"成功获取EDID数据，找到 {displayList.Count} 个显示设备");
                }
                else
                {
                    LogHelper.LogError("未能获取有效的EDID数据");
                }
            }
            catch (Exception ex)
            {
                LogHelper.LogError("获取EDID数据时出错", ex);
            }

            return edidFound ? (string[])displayList.ToArray(typeof(string)) : null;
        }

        private void PictureBox1_Click(object sender, EventArgs e)
        {
            Cursor = Cursors.WaitCursor;
            try
            {
                LogHelper.LogInfo("开始读取显示器EDID数据...");

                // 检查是否在VMware环境中
                bool isVMware = IsRunningInVMware();

                string[] display_id = DisplayList();
                if (display_id == null)
                {
                    string errorMsg = "未找到EDID数据。请确保显示器已正确连接。";
                    LogHelper.LogError(errorMsg);
                    return;
                }

                EDID_Tree(edid_data);

                // 显示成功消息
                if (isVMware)
                {
                    // 在VMware环境中显示特定消息
                    string vmwareMsg = "成功获取VMware虚拟显示器的EDID数据。\n注意：这可能是模拟的EDID数据。";
                    LogHelper.LogSuccess(vmwareMsg);
                }
                else if (display_id.Length > 1)
                {
                    // 在物理环境中显示找到的显示器数量
                    string successMsg = $"成功获取EDID数据。找到{display_id.Length}个显示设备。";
                    LogHelper.LogSuccess(successMsg);
                }
                else
                {
                    LogHelper.LogSuccess("成功获取EDID数据");
                }
            }
            catch (Exception ex)
            {
                LogHelper.LogError("读取EDID数据时出错", ex);
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }

        private void PictureBox2_Click(object sender, EventArgs e)
        {
            if (!edid_found)
            {
                string errorMsg = "没有可保存的EDID数据";
                LogHelper.LogError(errorMsg);
                return;
            }

            SaveFileDialog saveFile = new SaveFileDialog
            {
                Title = "Save File",
                Filter = "EDID file (*.bin)|*.bin",
                FilterIndex = 2,
                RestoreDirectory = true
            };

            if (saveFile.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    string save_path = Path.Combine(saveFile.InitialDirectory, saveFile.FileName);
                    FileStream dstStream = new FileStream(save_path, FileMode.Create, FileAccess.Write, FileShare.ReadWrite);
                    dstStream.Write(edid_data, 0, edid_data.Length);
                    dstStream.Flush();
                    dstStream.Close();
                }
                catch(Exception ex)
                {
                    LogHelper.LogError("保存EDID数据时出错", ex);
                }
            }
            else
            {
                LogHelper.LogInfo("用户取消了保存操作");
            }
        }

        private void PictureBox3_Click(object sender, EventArgs e)
        {
            AboutEx.Show(this);
        }

        private void PictureBox4_Click(object sender, EventArgs e)
        {
            OpenFileDialog dialog = new OpenFileDialog
            {
                Filter = "EDID file (*.bin)|*.bin",
                RestoreDirectory = true,
                Multiselect = false
            };

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                EDID_Proc(dialog.FileName);
            }
        }

        private void TreeView1_DragEnter(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                string[] file = (string[])e.Data.GetData(DataFormats.FileDrop);
                if (file.Length > 1)
                {
                    LogHelper.LogWarning("拖放了多个文件，不支持此操作");
                    e.Effect = DragDropEffects.None;
                }
                else
                {
                    LogHelper.LogInfo("文件拖放操作进行中...");
                    e.Effect = DragDropEffects.Copy;
                }
            }
        }

        private void TreeView1_DragDrop(object sender, DragEventArgs e)
        {
            string[] file = (string[])e.Data.GetData(DataFormats.FileDrop);

            if (file.Length > 1)
            {
                LogHelper.LogWarning("拖放了多个文件，忽略此操作");
                return;
            }

            LogHelper.LogInfo($"接收到拖放文件: {file[0]}");

            if (Path.GetExtension(file[0]) == ".bin")
            {
                EDID_Proc(file[0]);
            }
            else
            {
                string errorMsg = "非EDID文件格式";
                LogHelper.LogError(errorMsg);
            }
        }

        private void EDID_Parse(byte[] edid, TreeNode node)
        {
            try
            {
                textBox1.Clear();
                textBox2.Clear();
                dataGridView1.Rows.Clear();

                EDID_FUNC edid_func = new EDID_FUNC();
                edid_func.EDID_Parsetext(edid, node, textBox1, true);
                edid_func.EDID_Parsetext(edid, node, textBox2, false);
                edid_func.EDID_Parsedata(edid, node, dataGridView1);
            }
            catch (Exception ex)
            {
                LogHelper.LogError($"解析EDID节点时出错: {node.Text}", ex);
            }
        }

        private void TreeView1_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            if (e.Node.ImageKey != "NODE_BLOCK")
            {
                EDID_Parse(edid_data, e.Node);
            }
        }
    }
}
