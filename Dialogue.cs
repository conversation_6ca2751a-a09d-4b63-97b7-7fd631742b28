﻿
using EDID_Manager;
using System.Windows.Forms;

public class MessageBoxEx
{
    public static DialogResult Show(Form p_form, string text, string caption)
    {
        MsgBox form = new MsgBox()
        {
            Text = caption,
            StartPosition = FormStartPosition.Manual,
        };

        int[] parent_position = new int[2];

        parent_position[0] = p_form.Top + p_form.Height / 2;
        parent_position[1] = p_form.Left + p_form.Width / 2;

        form.Top = parent_position[0] - form.Height / 2;
        form.Left = parent_position[1] - form.Width / 2;
        form.Set_String(text);

        return form.ShowDialog();
    }
}

public class AboutEx
{
    public static DialogResult Show(Form p_form)
    {
        AboutBox form = new AboutBox()
        {
            StartPosition = FormStartPosition.Manual,
        };

        int[] parent_position = new int[2];

        parent_position[0] = p_form.Top + p_form.Height / 2;
        parent_position[1] = p_form.Left + p_form.Width / 2;

        form.Top = parent_position[0] - form.Height / 2;
        form.Left = parent_position[1] - form.Width / 2;
        return form.ShowDialog();
    }
}
