﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAABMLAAATCwAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAtoMrALaEKwCyiy8At4EqALiAKQC4fycAuH8nALmAKAC4gCkAunkqALSH
        JwO3gCYJt4AmC7WAJgbAdTQAuX8pALl/KQC3gigAqZAmALGIJwC6ficAuX8mALl/JwC5fycAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAALp/KgC5gCoAtYUsAPoWBADFaiMAuXwoALh/KQC3fiMCuYAoGLiA
        KTK5gClHuYApXrmAKGu5gChuuIApY7mAKVC5fyk6uX8pIreDJwi+eykAuoAnAL1/JgDUgicAuIEnALl9
        KACtyRgAtpEkAAAAAAAAAAAAAAAAAAAAAAC5fycAuX8oALp/KgC0gCAAvHspALSGKwC6dycEt38pJ7h/
        KV25gCmVuYApx7mAKei5gCnzuYAp9bmAKfW5gCn0uYAp7rl/KdW5gCmouIAocriAKDq8gCcNsoEqALt/
        JwCymCIAuXsqAMBZMQCxii8Asn8zAAAAAAAAAAAAun8qALl/KAC5fycAtoU2ALiBKQBz/3EAt4AqFrh/
        KVy4gCmtuYAp6LmAKfy5gCn/uYAp/7mAKf+5gCn/uYAp/7mAKf+5gCn/uYAp/7mAKf25gCn0uYApx7mA
        KHq5fyctuX4mA7l/KACyfTYAuYAmAOOHAACqkiQAqqoAALh/KgC5fyoAun8qALh/NAC6fyoAu34oArp/
        Kiq5gCmKuYAp3rmAKf65gCn/uYAp/7mAKf+5gCn/uYAp/7mAKf+5gCn/uYAp/7mAKf+5gCn/uYAp/7mA
        Kf+5gCn/uYAp8LmAKK65fyhLuIQnCLp9JwCxnxMAuIQjAMJnPgDYIYMAuH8qALeBKwC0dh8AuH8oALt/
        KAK5fygyuX8pobmAKfK5gCn/uYAp/7mAKf+5gCn/uYAp/7l/KP+5fyj/uX8o/7l/KP+5fyj/uX8o/7mA
        KP+5gCn/uYAp/7mAKf+5gCn/uYAp+7mAKci5fylatn8sCbp8LACmgzMAuHgwALp5LwC3eywA/xIAALx6
        KACmfEAAuX8qKrl/KaG5gCn2uYAp/7mAKf+5gCn/uYAp/7mAKf+6giz/u4Qw/7yFMf+8hTH/vIUx/7yF
        Mf+8hTH/u4Mu/7mAKv+5gCn/uYAp/7mAKf+5gCn/uYAp/rmAKcy4gClStYchBbmCKADHdTwAtoYiALeD
        KwDIZCAAtYUrALl/KRa4fymKuYAp8bmAKf+5gCn/uYAp/7mAKf+5gCn/u4Qv/8qeXf/WtoX/17iI/9e3
        iP/Xt4j/17eI/9e4iP/PqnD/v4w9/7mAKP+5gCn/uYAp/7mAKf+5gCn/uYAp/LmAKbq4gCg51p8yALqC
        KQC6gSoAzVsdALt7KAC7eikFuYApXLmAKd65gCn/uYAp/7mAKf+5gCn/uYAp/7mAKP+/izz/4Mik//j0
        7//49fD/+PXw//j18P/49fD/+fbx/+zeyv/Kn17/uX8o/7mAKf+5gCn/uYAp/7mAKf+5gCn/uYAp97mA
        KZG6gCgYuYAoALd/JwCfqjQAtoUqALl/KSm5gCmvuYAp/rmAKf+5gCn/uYAp/7mAKf+4fyf/uH4l/7qC
        LP/Srnf/6dnA/+zfy//t4Mz/7d/M/+3gzP/r3cj/3MCW/8GOQv+4fST/uH4m/7l/KP+5gCn/uYAp/7mA
        Kf+5gCn/uYAp3bl/KFKyfSMCt38nALl8JgC4dyMDuX8oX7mAKem5gCn/uYAp/7mAKf+5gCj/u4Ux/8ic
        WP/RrHT/0q53/9Syf//ZvI//1bSC/9Cqcf/PqXD/0ax1/9q9kf/WtoX/0q95/9Kud//NpWf/v4s8/7mA
        Kv+5gCn/uYAp/7mAKf+5gCn7uIAplreAKBm4gCgAuH8oALh/Jxq5gCiZuYAp/LmAKf+5gCn/uYAp/7mA
        Kf/GmVP/6tzF//n39P/7+ff/+/n3//j18P/kz6//5M+w/+fVuf/iy6j/8ejZ//r59v/7+ff/+/n3//Pr
        3//Usn7/vIUy/7l/KP+5gCn/uYAp/7mAKf+4gCnTuYAoO7N7LQC5fykAuIApNLmAKcq5gCn/uYAp/7mA
        Kf+5gCj/uoIs/9Cqcf/38+3//f7///z9/f/9/f7/+vj1/+TPr//z697/+fbx/+rawv/u4tD//Pz8//39
        /v/9/f7/+/n3/+PNrP+/izz/uH8n/7mAKf+5gCn/uYAp/7mAKfK5gClhvoUmBb54KgC4gClLuIAp6rmA
        Kf+5gCn/uYAp/7mAKP+6gy7/0a12//bx6v/38uz/8+zg//Ps4P/y6Nv/4Mij/+DIo//izKr/3MKZ/+ze
        yf/z6+D/8+vg//Tu5P/49fD/5NCx/7+MPf+4fyf/uYAp/7mAKf+5gCn/uYAp+LmAKYa5gCkVt4EpBbmA
        KWG5gCn0uYAp/7mAKf+5gCn/uYAo/7qDLf/Rrnf/8efZ/9zAl//Kn17/yZ5d/8meXP/ElU3/wY9C/8GP
        Qv/Bj0P/yZ1a/8qfXf/Jnlz/z6hu/+3hzv/l0rP/v4w9/7h/J/+5gCn/uYAp/7mAKf+5gCn7uYAporl/
        KSC4gCcLuYApb7mAKfW5gCn/uYAp/7mAKf+5gCj/uoMt/9Kud//u49H/0Ktz/7h/J/+4fiX/uH4m/7h/
        J/+4fyf/uH8n/7h/J/+4fib/uH4l/7h9Jf+/izz/6Ni//+XStP+/jD3/uH8n/7mAKf+5gCn/uYAp/7mA
        Kfy5gCmwuIAoJrl/Jwy5gChyuYAp9rmAKf+5gCn/uYAp/7mAKP+6gy3/0q53/+7j0v/RrXX/uoEr/7mA
        Kf+5gCn/uYAp/7mAKf+5gCn/uYAp/7mAKf+5gCn/uX8o/8CNP//p2cD/5dK0/7+MPf+4fyf/uYAp/7mA
        Kf+5gCn/uYAp/bmAKLO4gCgotn8nCLmAKWm5gCn1uYAp/7mAKf+5gCn/uYAo/7qDLf/Srnf/7uPS/9Gt
        df+6gSv/uYAp/7mAKf+5gCn/uYAp/7mAKf+5gCn/uYAp/7mAKf+5fyj/wI0//+nZwP/l0rT/v4w9/7h/
        J/+5gCn/uYAp/7mAKf+5gCn7uX8pqbl/KCQ4sQsAuIAoVbmAKfG5gCn/uYAp/7mAKf+5gCj/uoMt/9Ku
        d//u49L/0a11/7qBK/+5gCn/uYAp/7mAKf+5gCn/uYAp/7mAKf+5gCn/uYAp/7l/KP/AjT//6dnA/+XS
        tP+/jD3/uH8n/7mAKf+5gCn/uYAp/7mAKfm5gCmUuX8pGrx/KwC3gCg+uIAp27mAKf+5gCn/uYAp/7mA
        KP+6gy3/0q53/+7j0v/RrHT/uYEq/7l/KP+5gCj/uYAo/7mAKP+5gCj/uYAo/7mAKP+5gCj/uH8n/8CM
        Pv/p2cD/5dK0/7+MPf+4fyf/uYAp/7mAKf+5gCn/uYAp9rmAKXK5gScMuH8pALh/KSa5gCmwuYAp/rmA
        Kf+5gCn/uYAo/7qDLf/Rrnf/7+TT/9KueP+7gy//uoIs/7qCLf+6gi3/uoIt/7qCLf+6gi3/uoIt/7qC
        Lf+6giz/wY9D/+nawf/l0rT/v4w9/7h/J/+5gCn/uYAp/7mAKf+5gCnkuYAoS7l0MgC6fycAun8nC7l/
        KXi5gCn2uYAp/7mAKf+5gCn/uYEq/82lZ//w5tb/59W5/9zAlf/bv5T/28CU/9vAlP/bwJT/28CU/9vA
        lP/bwJT/28CU/9u/lP/fxqD/8ObW/97Gn/++iTj/uH8n/7mAKf+5gCn/uYAp/rmAKbS4gCgouYAoAMJ/
        JQCxfysAuX8oQLmAKcy5gCn/uYAp/7mAKf+5gCj/wY5A/9q8j//n1bn/6Ne8/+jXvP/o17z/6Ne8/+jX
        vP/o17z/6Ne8/+jXvP/o17z/6Ne8/+jWvP/hyqf/yZ1a/7qCLf+5gCn/uYAp/7mAKf+5gCnwuYApb7aC
        KQm3gikAvn8mALmAKQC4gSkQuIAogrmAKfO5gCn/uYAp/7mAKf+6gSv/vok4/8OSR//Dk0r/w5NK/8OT
        Sv/Dk0r/w5NK/8OTSv/Dk0r/w5NK/8OTSv/Dk0r/w5NJ/8CNQP+6gy3/uYAp/7mAKf+5gCn/uYAp/rmA
        Kba5gCkvvHslAK+RNAC2fygAuIIqALl0IAC4fygyuYAptbmAKfy5gCn/uYAp/7mAKf+5fyj/uH8m/7h+
        Jv+4fib/uH4m/7h+Jv+4fib/uH4m/7h+Jv+4fib/uH4m/7h+Jv+4fib/uH8n/7mAKf+5gCn/uYAp/7mA
        Kf+5gCneuIApXbV/KAW2gSkAtIctALx3JgCjvEIAuX8pAL11JQS5fyhRuYApzrmAKf65gCn/uYAp/7mA
        Kf+5gCn/uYAp/7mAKf+5gCn/uYAp/7mAKf+5gCn/uYAp/7mAKf+5gCn/uYAp/7mAKf+5gCn/uYAp/7mA
        Kf+5gCn/uYAp7LiAKIC5gSoUvIEoAK2BLwC/eycAtIYqALaFKgDDeScAtoMpALt/KAq5fyhfuYAp0LmA
        Kf25gCn/uYAp/7mAKf+5gCn/uYAp/7mAKf+5gCn/uYAp/7mAKf+5gCn/uYAp/7mAKf+5gCn/uYAp/7mA
        Kf+5gCn/uYAp/7mAKey5gCiMuIEoH7B2IAC1gSkAqH0mAMGMNQCfrDYAq5QuALl9JwDHbyQAtn8oALt/
        KQq5fylXuYApwLmAKfm5gCn/uYAp/7mAKf+5gCn/uYAp/7mAKf+5gCn/uYAp/7mAKf+5gCn/uYAp/7mA
        Kf+5gCn/uYAp/7mAKf65gCneuH8ogbh/KSC5gy0AuYArALiAKwC9gysAuoIrAP8AAADOayUA/41OALd9
        JwC1fyoAun8oALR/KAe4fyg/uYApmbmAKeO5gCn8uYAp/7mAKf+5gCn/uYAp/7mAKf+5gCn/uYAp/7mA
        Kf+5gCn/uYAp/7mAKf65gCnxuYApuLmAKF+3fykUyIUcALeAKQC5gCkAuIEoALmCKQC6gisAAAAAAL9/
        KgDBfysAAH0AALZ/KQC/fC8AuYAoAL6HJgG6gCgduYApWbiAKZ65gCnZuYAp9LmAKfm5gCn7uYAp/bmA
        Kf25gCn8uYAp+rmAKfa5gCnmuYApt7mAKHK4gCgxt4QnBrh7KQC3gykAt4MnALh/KgC4gCkAuIEoAAAA
        AAAAAAAAAAAAAL9/KgC3fyoA1X8kAL5+KADKgicAu30nALaFKgC5eicDt4AoHbiAKUC5gClouYApjbl/
        Kai5gCm1uYAouLh/KK25fyiYuYApd7mAKU65gCgquoEpCrZ6KAC4gigAtI8nALqPIwC1fCwAt38qALh/
        KgAAAAAAAAAAAAAAAAAAAAAAAAAAAKp/KgBsfzMAvX8nAK6LLADpJwwAqpgwALl+KAC4gCgAvHklALmB
        Kwi5fykYuX8oI7l/KSm5gCgquH8oJbh/KBy5gCkOmD8AALmAKQC6gSkAvY4nALuGKACydCsAqYMyALV/
        KgC2fyoAAAAAAAAAAAAAAAAA8AAAD+AAAAPAAAABgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAACAAAABwAAAA+AAAAc=
</value>
  </data>
</root>